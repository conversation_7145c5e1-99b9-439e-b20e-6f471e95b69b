{"Version": 3, "Meta": {"Duration": 5.93, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": false, "CurveCount": 33, "TotalSegmentCount": 190, "TotalPointCount": 537, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 18, 1, 0.211, 18, 0.422, 0, 0.633, 0, 1, 0.889, 0, 1.144, 22, 1.4, 22, 1, 1.578, 22, 1.756, 22, 1.933, 22, 1, 2.1, 22, 2.267, 14.992, 2.433, 0, 1, 2.6, -14.992, 2.767, -23, 2.933, -23, 1, 3.244, -23, 3.556, -23, 3.867, -23, 1, 4.078, -23, 4.289, 0, 4.5, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, -24, 1, 0.211, -24, 0.422, 0, 0.633, 0, 1, 0.889, 0, 1.144, -18, 1.4, -18, 1, 1.522, -18, 1.644, -11, 1.767, -11, 1, 1.822, -11, 1.878, -11, 1.933, -11, 1, 2.1, -11, 2.267, -3, 2.433, -3, 1, 2.6, -3, 2.767, -30, 2.933, -30, 1, 3.133, -30, 3.333, -23, 3.533, -23, 1, 3.644, -23, 3.756, -23, 3.867, -23, 1, 3.911, -23, 3.956, -23, 4, -23, 1, 4.167, -23, 4.333, 0, 4.5, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.756, 0, 0.844, 1.707, 0.933, 8, 1, 1.122, 21.373, 1.311, 30, 1.5, 30, 1, 1.656, 30, 1.811, 30, 1.967, 30, 1, 2.289, 30, 2.611, -30, 2.933, -30, 1, 3.244, -30, 3.556, -30, 3.867, -30, 1, 4.078, -30, 4.289, -21.337, 4.5, 1, 1, 4.578, 9.23, 4.656, 17, 4.733, 17, 0, 5.933, 17]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 1, 0, 5.933, 1]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.333, 1, 0.667, 1, 1, 1, 1, 1.044, 1, 1.089, 0, 1.133, 0, 1, 1.156, 0, 1.178, 0, 1.2, 0, 1, 1.256, 0, 1.311, 1, 1.367, 1, 1, 1.833, 1, 2.3, 1, 2.767, 1, 1, 2.867, 1, 2.967, 1.2, 3.067, 1.2, 1, 3.233, 1.2, 3.4, 1, 3.567, 1, 1, 3.689, 1, 3.811, 1, 3.933, 1, 1, 3.978, 1, 4.022, 0, 4.067, 0, 1, 4.089, 0, 4.111, 0, 4.133, 0, 1, 4.189, 0, 4.244, 1, 4.3, 1, 1, 4.4, 1, 4.5, 1, 4.6, 1, 1, 4.633, 1, 4.667, 0, 4.7, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0.63, 1, 1.433, 0.63, 2.867, 0.6, 4.3, 0.6, 1, 4.4, 0.6, 4.5, 0.771, 4.6, 0.771, 0, 5.933, 0.771]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.333, 1, 0.667, 1, 1, 1, 1, 1.044, 1, 1.089, 0, 1.133, 0, 1, 1.156, 0, 1.178, 0, 1.2, 0, 1, 1.256, 0, 1.311, 1, 1.367, 1, 1, 1.833, 1, 2.3, 1, 2.767, 1, 1, 2.867, 1, 2.967, 1.2, 3.067, 1.2, 1, 3.233, 1.2, 3.4, 1, 3.567, 1, 1, 3.689, 1, 3.811, 1, 3.933, 1, 1, 3.978, 1, 4.022, 0, 4.067, 0, 1, 4.089, 0, 4.111, 0, 4.133, 0, 1, 4.189, 0, 4.244, 1, 4.3, 1, 1, 4.4, 1, 4.5, 1, 4.6, 1, 1, 4.633, 1, 4.667, 0, 4.7, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0.63, 1, 1.433, 0.63, 2.867, 0.6, 4.3, 0.6, 1, 4.4, 0.6, 4.5, 0.76, 4.6, 0.76, 0, 5.933, 0.76]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, -0.002, 1, 0.211, -0.002, 0.422, 0, 0.633, 0, 1, 0.889, 0, 1.144, -0.31, 1.4, -0.31, 1, 1.578, -0.31, 1.756, -0.31, 1.933, -0.31, 1, 2.233, -0.31, 2.533, 0.67, 2.833, 0.67, 1, 3.144, 0.67, 3.456, 0.67, 3.767, 0.67, 1, 4.011, 0.67, 4.256, 0, 4.5, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 1, 0.211, 0, 0.422, 0, 0.633, 0, 1, 0.889, 0, 1.144, 0.38, 1.4, 0.38, 1, 1.578, 0.38, 1.756, 0.38, 1.933, 0.38, 1, 2.233, 0.38, 2.533, 0.46, 2.833, 0.46, 1, 3.144, 0.46, 3.456, 0.46, 3.767, 0.46, 1, 4.011, 0.46, 4.256, 0, 4.5, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 0.956, 0, 1.911, 0, 2.867, 0, 1, 2.933, 0, 3, 0.125, 3.067, 0.125, 1, 3.133, 0.125, 3.2, 0, 3.267, 0, 1, 3.711, 0, 4.156, 0, 4.6, 0, 1, 4.667, 0, 4.733, 0.396, 4.8, 0.396, 0, 5.933, 0.396]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 0.956, 0, 1.911, 0, 2.867, 0, 1, 2.933, 0, 3, 0.125, 3.067, 0.125, 1, 3.133, 0.125, 3.2, 0, 3.267, 0, 1, 3.711, 0, 4.156, 0, 4.6, 0, 1, 4.667, 0, 4.733, 0.417, 4.8, 0.417, 0, 5.933, 0.417]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0.03, 0, 5.933, 0.03]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, -1, 1, 1.433, -1, 2.867, -1, 4.3, -1, 1, 4.367, -1, 4.433, 1, 4.5, 1, 0, 5.933, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 1, 1, 1.433, 1, 2.867, 1, 4.3, 1, 1, 4.367, 1, 4.433, 0, 4.5, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.244, 0, 0.489, 0, 0.733, 0, 1, 0.778, 0, 0.822, 0.667, 0.867, 2, 1, 1.044, 7.333, 1.222, 10, 1.4, 10, 1, 1.533, 10, 1.667, 10, 1.8, 10, 1, 2.178, 10, 2.556, 6, 2.933, 6, 1, 3.244, 6, 3.556, 6, 3.867, 6, 1, 4.189, 6, 4.511, 0, 4.833, 0, 1, 4.956, 0, 5.078, 1.192, 5.2, 1.192, 1, 5.322, 1.192, 5.444, 0.013, 5.567, 0.013, 0, 5.933, 0.013]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.211, 0, 0.422, 0, 0.633, 0, 1, 0.667, 0, 0.7, 0, 0.733, 0, 1, 1.133, 0, 1.533, 0, 1.933, 0, 1, 2.078, 0, 2.222, 4, 2.367, 4, 1, 2.556, 4, 2.744, -2.18, 2.933, -2.18, 1, 3.022, -2.18, 3.111, 0, 3.2, 0, 1, 3.456, 0, 3.711, -1, 3.967, -1, 1, 4.067, -1, 4.167, -1.018, 4.267, 0, 1, 4.333, 0.679, 4.4, 10, 4.467, 10, 1, 4.567, 10, 4.667, -5, 4.767, -5, 1, 4.889, -5, 5.011, 0, 5.133, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.233, 0, 0.467, 0, 0.7, 0, 1, 0.733, 0, 0.767, 0, 0.8, 0, 1, 1.278, 0, 1.756, -4, 2.233, -4, 1, 2.544, -4, 2.856, 8, 3.167, 8, 1, 3.4, 8, 3.633, 8.008, 3.867, 7.985, 1, 4.178, 7.954, 4.489, -3, 4.8, -3, 0, 5.933, -3]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.122, 0, 0.244, 1, 0.367, 1, 1, 0.511, 1, 0.656, 0, 0.8, 0, 1, 1.022, 0, 1.244, 1, 1.467, 1, 1, 1.689, 1, 1.911, 0, 2.133, 0, 1, 2.344, 0, 2.556, 1, 2.767, 1, 1, 2.967, 1, 3.167, 0, 3.367, 0, 1, 3.578, 0, 3.789, 1, 4, 1, 1, 4.233, 1, 4.467, 0, 4.7, 0, 1, 4.9, 0, 5.1, 0, 5.3, 0, 0, 5.933, 0]}, {"Target": "Parameter", "Id": "ParamShoulder", "Segments": [0, 0, 1, 0.456, 0, 0.911, 0.4, 1.367, 0.4, 1, 1.889, 0.4, 2.411, -1, 2.933, -1, 1, 3.244, -1, 3.556, -1, 3.867, -1, 0, 5.933, -1]}, {"Target": "Parameter", "Id": "ParamLeg", "Segments": [0, 1, 0, 5.933, 1]}, {"Target": "Parameter", "Id": "ParamArmLA", "Segments": [0, -8.7, 1, 0.289, -8.7, 0.578, -8.7, 0.867, -8.7, 1, 1, -8.7, 1.133, -10, 1.267, -10, 1, 1.578, -10, 1.889, -10, 2.2, -10, 1, 2.444, -10, 2.689, -6.9, 2.933, -6.9, 1, 3.244, -6.9, 3.556, -6.9, 3.867, -6.9, 1, 3.989, -6.9, 4.111, -8.3, 4.233, -8.3, 1, 4.344, -8.3, 4.456, -3, 4.567, -3, 1, 4.689, -3, 4.811, -4, 4.933, -4, 1, 5.1, -4, 5.267, -3, 5.433, -3, 0, 5.933, -3]}, {"Target": "Parameter", "Id": "ParamArmRA", "Segments": [0, -8.7, 1, 0.289, -8.7, 0.578, -8.7, 0.867, -8.7, 1, 1, -8.7, 1.133, -10, 1.267, -10, 1, 1.578, -10, 1.889, -10, 2.2, -10, 1, 2.444, -10, 2.689, -10, 2.933, -10, 1, 3.244, -10, 3.556, -10, 3.867, -10, 1, 3.989, -10, 4.111, -9.998, 4.233, -7.8, 1, 4.344, -5.802, 4.456, 0.03, 4.567, 0.03, 1, 4.689, 0.03, 4.811, -1, 4.933, -1, 1, 5.122, -1, 5.311, -0.002, 5.5, -0.002, 0, 5.933, -0.002]}, {"Target": "Parameter", "Id": "ParamHandL", "Segments": [0, 0, 1, 1.067, 0, 2.133, 0, 3.2, 0, 1, 3.633, 0, 4.067, 1, 4.5, 1, 1, 4.667, 1, 4.833, -1, 5, -1, 1, 5.122, -1, 5.244, 0.208, 5.367, 0.208, 0, 5.933, 0.208]}, {"Target": "Parameter", "Id": "ParamHandR", "Segments": [0, 0, 1, 1.067, 0, 2.133, 0, 3.2, 0, 1, 3.633, 0, 4.067, 1, 4.5, 1, 1, 4.667, 1, 4.833, -1, 5, -1, 1, 5.122, -1, 5.244, 0.208, 5.367, 0.208, 0, 5.933, 0.208]}, {"Target": "Parameter", "Id": "ParamHairAhoge", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.133, 0, 1.267, 10, 1.4, 10, 1, 1.489, 10, 1.578, 2.177, 1.667, 2.177, 1, 1.789, 2.177, 1.911, 10, 2.033, 10, 1, 2.289, 10, 2.544, -10, 2.8, -10, 1, 2.933, -10, 3.067, 7.54, 3.2, 7.54, 1, 3.344, 7.54, 3.489, 0, 3.633, 0, 1, 3.911, 0, 4.189, 0, 4.467, 0, 1, 4.6, 0, 4.733, 10, 4.867, 10, 1, 4.989, 10, 5.111, -10, 5.233, -10, 1, 5.322, -10, 5.411, 3.456, 5.5, 3.456, 1, 5.556, 3.456, 5.611, 0, 5.667, 0, 0, 5.933, 0]}, {"Target": "PartOpacity", "Id": "PartArmA", "Segments": [0, 1, 0, 5.93, 1]}, {"Target": "PartOpacity", "Id": "PartArmB", "Segments": [0, 0, 0, 5.93, 0]}]}