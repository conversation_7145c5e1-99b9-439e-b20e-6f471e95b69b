{"Version": 3, "Meta": {"Duration": 10, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 37, "TotalSegmentCount": 232, "TotalPointCount": 659, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.178, 0, 0.356, -4, 0.533, -4, 1, 0.989, -4, 1.444, -4, 1.9, -4, 1, 2.378, -4, 2.856, 0, 3.333, 0, 1, 3.444, 0, 3.556, 0, 3.667, 0, 1, 4.411, 0, 5.156, 0, 5.9, 0, 1, 6.433, 0, 6.967, 14, 7.5, 14, 1, 7.833, 14, 8.167, 14, 8.5, 14, 1, 8.967, 14, 9.433, 0, 9.9, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.178, 0, 0.356, -6, 0.533, -6, 1, 0.989, -6, 1.444, -6, 1.9, -6, 1, 2.378, -6, 2.856, 0, 3.333, 0, 1, 3.444, 0, 3.556, 0, 3.667, 0, 1, 4.411, 0, 5.156, 0, 5.9, 0, 1, 6.211, 0, 6.522, 5, 6.833, 5, 1, 7.056, 5, 7.278, -6, 7.5, -6, 1, 7.833, -6, 8.167, -6, 8.5, -6, 1, 8.967, -6, 9.433, 0, 9.9, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.178, 0, 0.356, -6, 0.533, -6, 1, 0.733, -6, 0.933, 1.432, 1.133, 5, 1, 1.389, 9.559, 1.644, 10, 1.9, 10, 1, 2.378, 10, 2.856, -6, 3.333, -6, 1, 3.444, -6, 3.556, -6, 3.667, -6, 1, 4.156, -6, 4.644, 6, 5.133, 6, 1, 5.389, 6, 5.644, 6, 5.9, 6, 1, 6.211, 6, 6.522, 12, 6.833, 12, 1, 7.389, 12, 7.944, -1.84, 8.5, -6, 1, 8.967, -9.494, 9.433, -9, 9.9, -9, 0, 10, -9]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.633, 1, 1.267, 1, 1.9, 1, 1, 2, 1, 2.1, 1, 2.2, 1, 1, 2.244, 1, 2.289, 0, 2.333, 0, 1, 2.356, 0, 2.378, 0, 2.4, 0, 1, 2.456, 0, 2.511, 1, 2.567, 1, 1, 2.933, 1, 3.3, 1, 3.667, 1, 1, 3.711, 1, 3.756, 0, 3.8, 0, 1, 3.822, 0, 3.844, 0, 3.867, 0, 1, 3.922, 0, 3.978, 1, 4.033, 1, 1, 4.656, 1, 5.278, 1, 5.9, 1, 1, 6.433, 1, 6.967, 1, 7.5, 1, 1, 7.544, 1, 7.589, 0, 7.633, 0, 1, 7.656, 0, 7.678, 0, 7.7, 0, 1, 7.756, 0, 7.811, 1, 7.867, 1, 1, 8.078, 1, 8.289, 1, 8.5, 1, 0, 10, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 1, 0.633, 0, 1.267, 0, 1.9, 0, 1, 2.489, 0, 3.078, 0, 3.667, 0, 1, 4.411, 0, 5.156, 0, 5.9, 0, 1, 6.767, 0, 7.633, 0, 8.5, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.633, 1, 1.267, 1, 1.9, 1, 1, 2, 1, 2.1, 1, 2.2, 1, 1, 2.244, 1, 2.289, 0, 2.333, 0, 1, 2.356, 0, 2.378, 0, 2.4, 0, 1, 2.456, 0, 2.511, 1, 2.567, 1, 1, 2.933, 1, 3.3, 1, 3.667, 1, 1, 3.711, 1, 3.756, 0, 3.8, 0, 1, 3.822, 0, 3.844, 0, 3.867, 0, 1, 3.922, 0, 3.978, 1, 4.033, 1, 1, 4.656, 1, 5.278, 1, 5.9, 1, 1, 6.433, 1, 6.967, 1, 7.5, 1, 1, 7.544, 1, 7.589, 0, 7.633, 0, 1, 7.656, 0, 7.678, 0, 7.7, 0, 1, 7.756, 0, 7.811, 1, 7.867, 1, 1, 8.078, 1, 8.289, 1, 8.5, 1, 0, 10, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 1, 0.633, 0, 1.267, 0, 1.9, 0, 1, 2.489, 0, 3.078, 0, 3.667, 0, 1, 4.411, 0, 5.156, 0, 5.9, 0, 1, 6.767, 0, 7.633, 0, 8.5, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamEyeForm", "Segments": [0, 0, 1, 0.633, 0, 1.267, 0, 1.9, 0, 1, 2.489, 0, 3.078, 0, 3.667, 0, 1, 4.411, 0, 5.156, 0, 5.9, 0, 1, 6.767, 0, 7.633, 0, 8.5, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.178, 0, 0.356, 0.23, 0.533, 0.23, 1, 0.989, 0.23, 1.444, 0.23, 1.9, 0.23, 1, 2.378, 0.23, 2.856, -0.6, 3.333, -0.6, 1, 3.444, -0.6, 3.556, -0.6, 3.667, -0.6, 1, 3.789, -0.6, 3.911, 0, 4.033, 0, 1, 4.656, 0, 5.278, 0, 5.9, 0, 1, 6.211, 0, 6.522, -0.49, 6.833, -0.49, 1, 7.389, -0.49, 7.944, -0.49, 8.5, -0.49, 1, 8.967, -0.49, 9.433, 0, 9.9, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 1, 0.178, 0, 0.356, 0.25, 0.533, 0.25, 1, 0.989, 0.25, 1.444, 0.25, 1.9, 0.25, 1, 2.378, 0.25, 2.856, 0, 3.333, 0, 1, 3.444, 0, 3.556, 0, 3.667, 0, 1, 3.789, 0, 3.911, 0, 4.033, 0, 1, 4.656, 0, 5.278, 0, 5.9, 0, 1, 6.211, 0, 6.522, -0.13, 6.833, -0.13, 1, 7.056, -0.13, 7.278, 0.21, 7.5, 0.21, 1, 7.833, 0.21, 8.167, 0.21, 8.5, 0.21, 1, 8.967, 0.21, 9.433, 0, 9.9, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallForm", "Segments": [0, 0, 1, 0.633, 0, 1.267, 0, 1.9, 0, 1, 2.489, 0, 3.078, 0, 3.667, 0, 1, 4.411, 0, 5.156, 0, 5.9, 0, 1, 6.767, 0, 7.633, 0, 8.5, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 0.633, 0, 1.267, 0, 1.9, 0, 1, 2.489, 0, 3.078, 0, 3.667, 0, 1, 4.411, 0, 5.156, 0, 5.9, 0, 1, 6.767, 0, 7.633, 0, 8.5, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 0.633, 0, 1.267, 0, 1.9, 0, 1, 2.489, 0, 3.078, 0, 3.667, 0, 1, 4.411, 0, 5.156, 0, 5.9, 0, 1, 6.767, 0, 7.633, 0, 8.5, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 1, 0.633, 0, 1.267, 0, 1.9, 0, 1, 2.489, 0, 3.078, 0, 3.667, 0, 1, 4.411, 0, 5.156, 0, 5.9, 0, 1, 6.767, 0, 7.633, 0, 8.5, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 1, 0.633, 0, 1.267, 0, 1.9, 0, 1, 2.489, 0, 3.078, 0, 3.667, 0, 1, 4.411, 0, 5.156, 0, 5.9, 0, 1, 6.767, 0, 7.633, 0, 8.5, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 1, 0.633, 0, 1.267, 0, 1.9, 0, 1, 2.489, 0, 3.078, 0, 3.667, 0, 1, 4.411, 0, 5.156, 0, 5.9, 0, 1, 6.767, 0, 7.633, 0, 8.5, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 1, 0.633, 0, 1.267, 0, 1.9, 0, 1, 2.489, 0, 3.078, 0, 3.667, 0, 1, 4.411, 0, 5.156, 0, 5.9, 0, 1, 6.767, 0, 7.633, 0, 8.5, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 1, 0.633, 0, 1.267, 0, 1.9, 0, 1, 2.489, 0, 3.078, 0, 3.667, 0, 1, 4.411, 0, 5.156, 0, 5.9, 0, 1, 6.767, 0, 7.633, 0, 8.5, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 1, 0.633, 0, 1.267, 0, 1.9, 0, 1, 2.489, 0, 3.078, 0, 3.667, 0, 1, 4.411, 0, 5.156, 0, 5.9, 0, 1, 6.767, 0, 7.633, 0, 8.5, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 1, 0.233, 0, 0.467, 0, 0.7, 0, 1, 0.844, 0, 0.989, 0.5, 1.133, 0.5, 1, 1.389, 0.5, 1.644, 0.5, 1.9, 0.5, 1, 2.489, 0.5, 3.078, 0.5, 3.667, 0.5, 1, 4.411, 0.5, 5.156, 0.5, 5.9, 0.5, 1, 6.433, 0.5, 6.967, 0.5, 7.5, 0.5, 1, 7.633, 0.5, 7.767, 0, 7.9, 0, 1, 8.1, 0, 8.3, 0, 8.5, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.233, 0, 0.467, 0, 0.7, 0, 1, 0.844, 0, 0.989, 0, 1.133, 0, 1, 1.389, 0, 1.644, 0, 1.9, 0, 1, 2.489, 0, 3.078, 0, 3.667, 0, 1, 4.411, 0, 5.156, 0, 5.9, 0, 1, 6.567, 0, 7.233, 0, 7.9, 0, 1, 8.1, 0, 8.3, 0, 8.5, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamTere", "Segments": [0, 0, 1, 0.633, 0, 1.267, 0, 1.9, 0, 1, 2.489, 0, 3.078, 0, 3.667, 0, 1, 4.411, 0, 5.156, 0, 5.9, 0, 1, 6.767, 0, 7.633, 0, 8.5, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.178, 0, 0.356, -3, 0.533, -3, 1, 0.989, -3, 1.444, -3, 1.9, -3, 1, 1.989, -3, 2.078, -3, 2.167, -3, 1, 2.556, -3, 2.944, 0, 3.333, 0, 1, 3.444, 0, 3.556, 0, 3.667, 0, 1, 3.722, 0, 3.778, 0, 3.833, 0, 1, 4.267, 0, 4.7, 4, 5.133, 4, 1, 5.389, 4, 5.644, 4, 5.9, 4, 1, 6, 4, 6.1, 4, 6.2, 4, 1, 6.411, 4, 6.622, 4.702, 6.833, 6, 1, 7.056, 7.367, 7.278, 8, 7.5, 8, 1, 7.833, 8, 8.167, 8, 8.5, 8, 1, 8.6, 8, 8.7, 8, 8.8, 8, 1, 9.167, 8, 9.533, 1, 9.9, 1, 0, 10, 1]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.633, 0, 1.267, 0, 1.9, 0, 1, 1.989, 0, 2.078, 0, 2.167, 0, 1, 2.667, 0, 3.167, 0, 3.667, 0, 1, 3.722, 0, 3.778, 0, 3.833, 0, 1, 4.522, 0, 5.211, 0, 5.9, 0, 1, 6, 0, 6.1, 0, 6.2, 0, 1, 6.967, 0, 7.733, 0, 8.5, 0, 1, 8.6, 0, 8.7, 0, 8.8, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.378, 0, 0.756, 4, 1.133, 4, 1, 1.389, 4, 1.644, 3, 1.9, 3, 1, 1.989, 3, 2.078, 3, 2.167, 3, 1, 2.556, 3, 2.944, 0, 3.333, 0, 1, 3.444, 0, 3.556, 0, 3.667, 0, 1, 3.722, 0, 3.778, 0, 3.833, 0, 1, 4.267, 0, 4.7, -2, 5.133, -2, 1, 5.389, -2, 5.644, -2, 5.9, -2, 1, 6, -2, 6.1, -2, 6.2, -2, 1, 6.411, -2, 6.622, -4, 6.833, -4, 1, 7.056, -4, 7.278, -2, 7.5, -2, 1, 7.833, -2, 8.167, -5, 8.5, -5, 1, 8.6, -5, 8.7, -5, 8.8, -5, 1, 9.167, -5, 9.533, 0, 9.9, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.633, 0, 1.267, 0, 1.9, 0, 1, 2.489, 0, 3.078, 0, 3.667, 0, 1, 4.411, 0, 5.156, 0, 5.9, 0, 1, 6.767, 0, 7.633, 0, 8.5, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamArmLA", "Segments": [0, 0.5, 0, 10, 0.5]}, {"Target": "Parameter", "Id": "ParamArmRA", "Segments": [0, 0.5, 0, 10, 0.5]}, {"Target": "Parameter", "Id": "ParamArmLB", "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamArmRB", "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamBustY", "Segments": [0, 0, 1, 0.633, 0, 1.267, 0, 1.9, 0, 1, 2.489, 0, 3.078, 0, 3.667, 0, 1, 4.411, 0, 5.156, 0, 5.9, 0, 1, 6.767, 0, 7.633, 0, 8.5, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 1, 0.633, 0, 1.267, 0, 1.9, 0, 1, 2.489, 0, 3.078, 0, 3.667, 0, 1, 4.411, 0, 5.156, 0, 5.9, 0, 1, 6.767, 0, 7.633, 0, 8.5, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 1, 0.633, 0, 1.267, 0, 1.9, 0, 1, 2.489, 0, 3.078, 0, 3.667, 0, 1, 4.411, 0, 5.156, 0, 5.9, 0, 1, 6.767, 0, 7.633, 0, 8.5, 0, 0, 10, 0]}, {"Target": "PartOpacity", "Id": "PARTS_01_ARM_L_A_001", "Segments": [0, 1, 0, 10, 1]}, {"Target": "PartOpacity", "Id": "PARTS_01_ARM_R_A_001", "Segments": [0, 1, 0, 10, 1]}, {"Target": "PartOpacity", "Id": "PARTS_01_ARM_L_B_001", "Segments": [0, 0, 0, 10, 0]}, {"Target": "PartOpacity", "Id": "PARTS_01_ARM_R_B_001", "Segments": [0, 0, 0, 10, 0]}]}