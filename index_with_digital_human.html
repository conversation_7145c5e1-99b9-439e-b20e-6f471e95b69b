<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>龙马科技-鸿源技术学校AI助手 - 数字人版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            height: 100vh;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .container {
            display: flex;
            height: 95vh;
            max-width: 95%;
            width: 95%;
            margin: 0 auto;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }

        /* 左侧面板 - 原有内容 */
        .left-panel {
            flex: 2;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px 0 0 20px;
            padding: 20px;
            display: flex;
            flex-direction: column;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            animation: slideInLeft 0.6s ease-out;
        }

        /* 中间聊天面板 */
        .chat-panel {
            flex: 3;
            display: flex;
            flex-direction: column;
            padding: 20px;
            background: #f8f9fa;
            min-width: 0;
            position: relative;
            animation: slideInUp 0.6s ease-out 0.2s both;
        }

        /* 右侧数字人面板 */
        .digital-human-panel {
            flex: 2;
            background: linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.1));
            border-radius: 0 20px 20px 0;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            animation: slideInRight 0.6s ease-out 0.1s both;
        }

        /* 数字人容器 */
        .digital-human-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 10;
        }

        /* 数字人占位符 */
        .digital-human-placeholder {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 80px;
            animation: float 3s ease-in-out infinite;
            z-index: 15;
        }

        /* 明学明事明德明志 艺术字样式 */
        .mingxue-art {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 18px;
            margin-bottom: 18px;
            margin-top: 2px;
        }

        .mingxue-char {
            font-family: 'STXingkai', 'KaiTi', 'FZShuTi', 'FZYaoti', '华文行楷', cursive, serif;
            font-size: 2.1rem;
            color: #764ba2;
            font-weight: bold;
            letter-spacing: 2px;
            text-shadow: 2px 2px 8px #e1e8ed, 0 2px 0 #fff, 0 0 8px #667eea44;
            transition: transform 0.2s;
        }

        .mingxue-char:hover {
            color: #1e3c72;
            transform: scale(1.08) rotate(-2deg);
        }

        /* 功能介绍 */
        .feature-intro {
            width: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 20px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .feature-intro .intro-text {
            color: white;
            font-size: 13px;
            line-height: 1.4;
            font-weight: 500;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            position: relative;
            z-index: 1;
        }

        /* 用户信息 */
        .user-info {
            margin-top: 20px;
            width: 100%;
        }

        .user-info h3 {
            color: #2c3e50;
            font-size: 16px;
            margin-bottom: 15px;
            text-align: center;
            font-weight: 600;
        }

        .info-input {
            width: 100%;
            padding: 12px 16px;
            margin-bottom: 12px;
            border: 2px solid #e1e8ed;
            border-radius: 12px;
            font-size: 14px;
            outline: none;
            background: #ffffff;
            transition: all 0.3s ease;
            color: #2c3e50;
        }

        .info-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        /* 聊天区域 */
        .chat-header {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 20px;
            background: url(pic/bg1.jpg);
            background-size: cover;
            height: 150px;
            padding: 20px;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .chat-messages {
            flex: 1;
            background: #ffffff;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 20px;
            overflow-y: auto;
            max-height: calc(100vh - 200px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border: 1px solid #e1e8ed;
        }

        .chat-input-container {
            display: flex;
            gap: 12px;
            align-items: center;
            background: rgba(255, 255, 255, 0.9);
            padding: 16px;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .chat-input {
            flex: 1;
            padding: 16px 20px;
            border: 2px solid #e1e8ed;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            background: #ffffff;
            transition: all 0.3s ease;
            color: #2c3e50;
        }

        .send-button {
            padding: 16px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .send-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
        }

        /* 动画 */
        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes float {
            0%, 100% {
                transform: translate(-50%, -50%) translateY(0px);
            }
            50% {
                transform: translate(-50%, -50%) translateY(-10px);
            }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                flex-direction: column;
                height: calc(100vh - 20px);
            }

            .left-panel {
                flex: none;
                height: auto;
                min-height: 200px;
                border-radius: 20px 20px 0 0;
            }

            .chat-panel {
                flex: 1;
                border-radius: 0;
            }

            .digital-human-panel {
                flex: none;
                height: 200px;
                border-radius: 0 0 20px 20px;
            }

            .digital-human-placeholder {
                font-size: 40px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 左侧面板 -->
        <div class="left-panel">
            <!-- 明学明事明德明志 艺术字 -->
            <div class="mingxue-art">
                <span class="mingxue-char">明学</span>
                <span class="mingxue-char">明事</span>
                <span class="mingxue-char">明德</span>
                <span class="mingxue-char">明志</span>
            </div>

            <div class="feature-intro">
                <div class="intro-text">
                    我可以和你聊天也可以给你进行专业推荐，点击下方按钮快速开始！
                </div>
            </div>

            <!-- 快捷功能按钮 -->
            <div style="display: flex; flex-direction: column; gap: 10px; margin-bottom: 20px;">
                <button onclick="insertText('专业推荐')" style="
                    padding: 12px 16px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border: none;
                    border-radius: 8px;
                    cursor: pointer;
                    font-size: 14px;
                    font-weight: 600;
                    transition: all 0.3s ease;
                    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
                " onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                    🎯 专业推荐
                </button>
                <button onclick="insertText('学校介绍')" style="
                    padding: 12px 16px;
                    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
                    color: white;
                    border: none;
                    border-radius: 8px;
                    cursor: pointer;
                    font-size: 14px;
                    font-weight: 600;
                    transition: all 0.3s ease;
                    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
                " onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                    🏫 学校介绍
                </button>
                <button onclick="insertText('招生咨询')" style="
                    padding: 12px 16px;
                    background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%);
                    color: white;
                    border: none;
                    border-radius: 8px;
                    cursor: pointer;
                    font-size: 14px;
                    font-weight: 600;
                    transition: all 0.3s ease;
                    box-shadow: 0 2px 8px rgba(253, 126, 20, 0.3);
                " onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                    📞 招生咨询
                </button>
            </div>

            <div class="user-info">
                <h3>用户信息</h3>
                <input type="text" class="info-input" id="userName" placeholder="请输入您的姓名">
                <input type="text" class="info-input" id="userPhone" placeholder="请输入您的手机号">
                <input type="text" class="info-input" id="userIdCard" placeholder="请输入您的身份证号">
            </div>
        </div>

        <!-- 中间聊天面板 -->
        <div class="chat-panel">
            <div class="chat-header">
                <img src="pic/school_name.png" alt="鸿源技术学校AI助手" style="max-width: 100%; height: 60px; display: block; margin: 0 auto; object-fit: contain;" />
            </div>

            <div class="chat-messages" id="chatMessages">
                <div style="padding: 20px; text-align: center; color: #666;">
                    👋 您好！我是鸿源技术学校AI助手，很高兴为您服务！<br>
                    右侧是我的数字人形象，您可以与我进行对话交流。
                </div>
            </div>

            <div class="chat-input-container">
                <input type="text" class="chat-input" id="chatInput" placeholder="请输入您的问题或想法..." />
                <button class="send-button" id="sendButton">发送</button>
            </div>
        </div>

        <!-- 右侧数字人面板 -->
        <div class="digital-human-panel">
            <div class="digital-human-container" id="digitalHumanContainer">
                <!-- 数字人占位符 -->
                <div class="digital-human-placeholder">🤖</div>
            </div>
        </div>
    </div>

    <!-- 数字人相关脚本 -->
    <script src="web/dist/bundle.js"></script>
    <script>
        // 数字人初始化函数
        function initDigitalHuman() {
            const container = document.getElementById('digitalHumanContainer');
            if (container) {
                console.log('初始化数字人容器');

                // 创建canvas用于Live2D渲染
                const canvas = document.createElement('canvas');
                canvas.id = 'live2dCanvas';
                canvas.style.width = '100%';
                canvas.style.height = '100%';
                canvas.style.position = 'absolute';
                canvas.style.top = '0';
                canvas.style.left = '0';
                canvas.style.zIndex = '10';

                // 移除占位符
                const placeholder = container.querySelector('.digital-human-placeholder');
                if (placeholder) {
                    placeholder.remove();
                }

                container.appendChild(canvas);

                // 尝试初始化Live2D
                try {
                    if (typeof window.LAppDelegate !== 'undefined') {
                        console.log('初始化Live2D数字人');
                        // 这里可以调用Live2D的初始化代码
                        // window.LAppDelegate.getInstance().initialize();
                        // window.LAppDelegate.getInstance().run();
                    } else {
                        console.log('Live2D库未加载完成，保持占位符');
                        // 重新添加占位符
                        const newPlaceholder = document.createElement('div');
                        newPlaceholder.className = 'digital-human-placeholder';
                        newPlaceholder.textContent = '🤖';
                        container.appendChild(newPlaceholder);
                    }
                } catch (error) {
                    console.error('Live2D初始化失败:', error);
                }
            }
        }

        // 聊天功能
        function initChat() {
            const chatInput = document.getElementById('chatInput');
            const sendButton = document.getElementById('sendButton');
            const chatMessages = document.getElementById('chatMessages');

            // 生成UUID作为session_id
            function generateUUID() {
                return 'session_' + 'xxxxxxxxxxxxxxxx'.replace(/[x]/g, function() {
                    return (Math.random() * 16 | 0).toString(16);
                });
            }

            const sessionId = generateUUID();

            function addMessage(content, isUser = false) {
                const messageDiv = document.createElement('div');
                messageDiv.style.cssText = `
                    margin: 15px 0;
                    display: flex;
                    ${isUser ? 'justify-content: flex-end;' : 'justify-content: flex-start;'}
                `;

                const bubbleDiv = document.createElement('div');
                bubbleDiv.style.cssText = `
                    max-width: 70%;
                    padding: 12px 16px;
                    border-radius: 18px;
                    ${isUser ?
                        'background: #667eea; color: white; margin-left: 10px;' :
                        'background: #f0f0f0; color: #333; margin-right: 10px; border: 1px solid #e0e0e0;'
                    }
                    word-wrap: break-word;
                    line-height: 1.4;
                `;

                bubbleDiv.innerHTML = content.replace(/\n/g, '<br>');
                messageDiv.appendChild(bubbleDiv);
                chatMessages.appendChild(messageDiv);
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }

            function sendMessage() {
                const message = chatInput.value.trim();
                if (message) {
                    // 添加用户消息
                    addMessage(message, true);
                    chatInput.value = '';

                    // 模拟AI回复
                    setTimeout(() => {
                        let response = '';
                        if (message.includes('专业推荐') || message.includes('推荐专业')) {
                            response = `🎯 <strong>专业推荐分析</strong><br><br>
                            根据您的情况，我为您推荐以下专业：<br><br>
                            🥇 <strong>计算机应用技术</strong> (适配率：85%)<br>
                            • 就业前景广阔，薪资待遇优厚<br>
                            • 技能实用性强，社会需求量大<br><br>
                            🥈 <strong>电子商务</strong> (适配率：78%)<br>
                            • 结合互联网发展趋势<br>
                            • 创业机会多，发展空间大<br><br>
                            🥉 <strong>汽车维修</strong> (适配率：72%)<br>
                            • 传统技能，稳定就业<br>
                            • 实践性强，技能扎实<br><br>
                            如需了解更多详情，请告诉我您感兴趣的专业！`;
                        } else if (message.includes('学校介绍') || message.includes('学校信息')) {
                            response = `🏫 <strong>鸿源技术学校介绍</strong><br><br>
                            <strong>明学 明事 明德 明志</strong><br><br>
                            🌟 <strong>学校特色</strong><br>
                            • 现代化教学设施，实训设备先进<br>
                            • 专业师资团队，经验丰富<br>
                            • 完善体育设施，全面发展<br>
                            • 就业保障体系，毕业即就业<br>
                            • 个性化培养方案，因材施教<br><br>
                            🎯 <strong>办学理念</strong><br>
                            培养技能人才，成就美好未来<br><br>
                            📞 如需了解更多，请选择"招生咨询"`;
                        } else if (message.includes('招生咨询') || message.includes('咨询')) {
                            response = `📞 <strong>招生咨询信息</strong><br><br>
                            🏫 <strong>学校地址</strong><br>
                            广东省珠海市香洲区<br><br>
                            📱 <strong>联系方式</strong><br>
                            • 咨询电话：13800138000<br>
                            • 邮箱：<EMAIL><br>
                            • 官网：www.hongyuan.edu.cn<br><br>
                            💻 <strong>技术支持</strong><br>
                            珠海龙马科技提供技术支持<br><br>
                            🕒 <strong>咨询时间</strong><br>
                            周一至周五：8:00-18:00<br>
                            周六至周日：9:00-17:00<br><br>
                            欢迎随时联系我们！`;
                        } else if (message.includes('你好') || message.includes('您好')) {
                            response = '👋 您好！我是鸿源技术学校的AI助手，很高兴为您服务！<br><br>我可以为您提供：<br>• 专业推荐和咨询<br>• 学校信息介绍<br>• 招生政策解答<br><br>请问有什么可以帮助您的吗？';
                        } else {
                            response = '感谢您的提问！我是鸿源技术学校的AI助手。<br><br>我可以为您提供专业推荐、学校介绍等服务。您可以点击左侧的快捷按钮或直接输入问题。<br><br>还有什么其他问题吗？';
                        }

                        addMessage(response, false);
                    }, 1000);
                }
            }

            sendButton.addEventListener('click', sendMessage);
            chatInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });
        }

        // 插入文本到输入框的函数
        function insertText(text) {
            const chatInput = document.getElementById('chatInput');
            if (chatInput) {
                chatInput.value = text;
                chatInput.focus();
            }
        }

        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            initDigitalHuman();
            initChat();
        });
    </script>
</body>
</html>
