<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字人集成页面启动器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
        }

        .container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            max-width: 600px;
            width: 90%;
        }

        h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .description {
            font-size: 1.2rem;
            margin-bottom: 30px;
            line-height: 1.6;
            opacity: 0.9;
        }

        .options {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin-bottom: 30px;
        }

        .option-button {
            padding: 20px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 15px;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            text-decoration: none;
            display: block;
        }

        .option-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        }

        .option-button.secondary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
        }

        .option-button.secondary:hover {
            box-shadow: 0 12px 35px rgba(40, 167, 69, 0.4);
        }

        .note {
            font-size: 0.9rem;
            opacity: 0.7;
            margin-top: 20px;
            line-height: 1.5;
        }

        .features {
            text-align: left;
            margin: 20px 0;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
        }

        .features h3 {
            margin-bottom: 15px;
            color: #fff;
        }

        .features ul {
            list-style: none;
            padding: 0;
        }

        .features li {
            margin: 8px 0;
            padding-left: 20px;
            position: relative;
        }

        .features li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 数字人集成页面</h1>
        <div class="description">
            欢迎使用鸿源技术学校AI助手数字人版本！<br>
            我们已经将原有的页面功能与数字人完美结合。
        </div>

        <div class="features">
            <h3>🌟 主要功能</h3>
            <ul>
                <li>智能专业推荐系统</li>
                <li>实时聊天对话功能</li>
                <li>Live2D数字人展示</li>
                <li>学校信息介绍</li>
                <li>招生咨询服务</li>
                <li>响应式设计，支持移动端</li>
            </ul>
        </div>

        <div class="options">
            <a href="index_with_digital_human.html" class="option-button">
                🚀 启动数字人集成页面
            </a>
            <a href="index.html" class="option-button secondary">
                📄 查看原始页面
            </a>
        </div>

        <div class="note">
            <strong>使用说明：</strong><br>
            1. 点击"启动数字人集成页面"体验完整功能<br>
            2. 数字人显示在页面右侧，支持Live2D动画<br>
            3. 左侧提供快捷功能按钮，中间是聊天区域<br>
            4. 如果数字人库未加载，会显示占位符图标<br>
            5. 支持专业推荐、学校介绍、招生咨询等功能
        </div>
    </div>

    <script>
        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const buttons = document.querySelectorAll('.option-button');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    // 添加点击效果
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });
        });
    </script>
</body>
</html>
