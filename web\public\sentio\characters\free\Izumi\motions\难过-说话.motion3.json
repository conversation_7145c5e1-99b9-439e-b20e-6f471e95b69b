{"Version": 3, "Meta": {"Duration": 3.867, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 29, "TotalSegmentCount": 146, "TotalPointCount": 359, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, -15, 1, 0.144, -15, 0.289, -15, 0.433, -15, 1, 0.8, -15, 1.167, -15, 1.533, -15, 1, 1.778, -15, 2.022, -20, 2.267, -20, 0, 3.867, -20]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.144, 0, 0.289, 0, 0.433, 0, 1, 0.8, 0, 1.167, 0, 1.533, 0, 1, 1.778, 0, 2.022, -11, 2.267, -11, 0, 3.867, -11]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 16, 1, 0.144, 16, 0.289, 16, 0.433, 16, 1, 0.7, 16, 0.967, -13, 1.233, -13, 1, 1.333, -13, 1.433, -13, 1.533, -13, 1, 1.778, -13, 2.022, -19, 2.267, -19, 0, 3.867, -19]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 0, 0.7, 1, 1, 0.744, 1.008, 0.789, -0.008, 0.833, 0, 0, 0.9, 0, 1, 0.956, -0.008, 1.011, 1.008, 1.067, 1, 0, 3.8, 1, 0, 3.867, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 1, 0.144, 0, 0.289, 0, 0.433, 0, 1, 0.8, 0, 1.167, 0, 1.533, 0, 0, 3.867, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 0, 0.7, 1, 1, 0.744, 1.008, 0.789, -0.008, 0.833, 0, 0, 0.9, 0, 1, 0.956, -0.008, 1.011, 1.008, 1.067, 1, 0, 3.8, 1, 0, 3.867, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 1, 0.144, 0, 0.289, 0, 0.433, 0, 1, 0.8, 0, 1.167, 0, 1.533, 0, 0, 3.867, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0.7, 1, 0.144, 0.7, 0.289, 0.7, 0.433, 0.7, 1, 0.8, 0.7, 1.167, 0.7, 1.533, 0.7, 1, 1.778, 0.7, 2.022, 0.79, 2.267, 0.79, 0, 3.867, 0.79]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 1, 0.144, 0, 0.289, 0, 0.433, 0, 1, 0.8, 0, 1.167, 0, 1.533, 0, 1, 1.778, 0, 2.022, 0.42, 2.267, 0.42, 0, 3.867, 0.42]}, {"Target": "Parameter", "Id": "ParamEyeBallForm", "Segments": [0, 0, 1, 0.144, 0, 0.289, 0, 0.433, 0, 1, 0.8, 0, 1.167, 0, 1.533, 0, 0, 3.867, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0.2, 1, 0.144, 0.2, 0.289, 0.2, 0.433, 0.2, 1, 0.8, 0.2, 1.167, 0.2, 1.533, 0.2, 0, 3.867, 0.2]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0.26, 1, 0.144, 0.26, 0.289, 0.26, 0.433, 0.26, 1, 0.8, 0.26, 1.167, 0.26, 1.533, 0.26, 0, 3.867, 0.26]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 1, 0.144, 0, 0.289, 0, 0.433, 0, 1, 0.8, 0, 1.167, 0, 1.533, 0, 0, 3.867, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 1, 0.144, 0, 0.289, 0, 0.433, 0, 1, 0.8, 0, 1.167, 0, 1.533, 0, 0, 3.867, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0.43, 1, 0.144, 0.43, 0.289, 0.43, 0.433, 0.43, 1, 0.7, 0.43, 0.967, 0.73, 1.233, 0.73, 1, 1.333, 0.73, 1.433, 0.73, 1.533, 0.73, 0, 3.867, 0.73]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0.47, 1, 0.144, 0.47, 0.289, 0.47, 0.433, 0.47, 1, 0.7, 0.47, 0.967, 0.79, 1.233, 0.79, 1, 1.333, 0.79, 1.433, 0.79, 1.533, 0.79, 0, 3.867, 0.79]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0.43, 1, 0.144, 0.43, 0.289, 0.43, 0.433, 0.43, 1, 0.7, 0.43, 0.967, -0.63, 1.233, -0.63, 1, 1.333, -0.63, 1.433, -0.63, 1.533, -0.63, 0, 3.867, -0.63]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0.4, 1, 0.144, 0.4, 0.289, 0.4, 0.433, 0.4, 1, 0.7, 0.4, 0.967, -0.68, 1.233, -0.68, 1, 1.333, -0.68, 1.433, -0.68, 1.533, -0.68, 0, 3.867, -0.68]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 1, 0.144, 0, 0.289, 0, 0.433, 0, 1, 0.7, 0, 0.967, 0.88, 1.233, 0.88, 1, 1.333, 0.88, 1.433, 0.88, 1.533, 0.88, 0, 3.867, 0.88]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 0, 0.467, 0, 1, 0.478, -0.016, 0.489, 0.356, 0.5, 0.675, 1, 0.511, 1.255, 0.522, 0.9, 0.533, 1, 0, 0.6, 1, 1, 0.611, 1.075, 0.622, 0.922, 0.633, 0.57, 1, 0.644, 0.292, 0.656, -0.013, 0.667, 0, 0, 0.8, 0, 1, 0.833, -0.017, 0.867, 0.745, 0.9, 1, 0, 1.1, 1, 1, 1.122, 0.83, 1.144, 0.722, 1.167, 0.346, 1, 1.189, -0.051, 1.211, 0.095, 1.233, 0.054, 1, 1.289, 0.041, 1.344, 0.03, 1.4, 0.022, 1, 1.511, 0.006, 1.622, 0, 1.733, 0, 1, 1.767, 0.098, 1.8, -0.271, 1.833, 0.968, 3, 1.867, 1, 0, 2, 1, 2, 2.033, 0.819, 1, 2.044, 0.476, 2.056, 0.101, 2.067, 0.118, 1, 2.078, 0.1, 2.089, 0.523, 2.1, 0.886, 3, 2.133, 1, 0, 2.2, 1, 1, 2.222, 0.519, 2.244, 0.155, 2.267, 0.149, 1, 2.278, 0.137, 2.289, 0.43, 2.3, 0.682, 1, 2.311, 1.071, 2.322, 0.991, 2.333, 1, 1, 2.344, 1.009, 2.356, 1.042, 2.367, 0.643, 1, 2.378, 0.373, 2.389, 0.057, 2.4, 0.071, 1, 2.411, 0.051, 2.422, 0.511, 2.433, 0.906, 3, 2.467, 1, 0, 2.5, 1, 2, 2.533, 0.886, 1, 2.556, 0.365, 2.578, 0.195, 2.6, 0.188, 1, 2.611, 0.173, 2.622, 0.523, 2.633, 0.824, 3, 2.667, 1, 2, 2.7, 0.965, 1, 2.711, 0.731, 2.722, 0.459, 2.733, 0.471, 1, 2.756, 0.471, 2.778, 0.463, 2.8, 0.533, 1, 2.822, 0.607, 2.844, 0.835, 2.867, 0.839, 1, 2.911, 0.843, 2.956, 0.373, 3, 0.376, 1, 3.011, 0.303, 3.022, 0.929, 3.033, 1, 0, 3.1, 1, 1, 3.133, 0.015, 3.167, 0.04, 3.2, 0.055, 1, 3.222, 0.064, 3.244, 0.553, 3.267, 0.714, 1, 3.289, 0.867, 3.311, 0.829, 3.333, 0.988, 3, 3.367, 1, 0, 3.433, 1, 2, 3.467, 0.918, 1, 3.489, 0.446, 3.511, 0.011, 3.533, 0, 0, 3.8, 0, 0, 3.867, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, -5, 1, 0.144, -5, 0.289, -5, 0.433, -5, 1, 0.711, -5, 0.989, -8, 1.267, -8, 1, 1.356, -8, 1.444, -8, 1.533, -8, 0, 3.867, -8]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.144, 0, 0.289, 0, 0.433, 0, 1, 0.8, 0, 1.167, 0, 1.533, 0, 0, 3.867, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.144, 0, 0.289, 0, 0.433, 0, 1, 0.8, 0, 1.167, 0, 1.533, 0, 1, 1.778, 0, 2.022, -3, 2.267, -3, 0, 3.867, -3]}, {"Target": "Parameter", "Id": "ParamArmL", "Segments": [0, 0, 1, 0.144, 0, 0.289, 0, 0.433, 0, 1, 0.8, 0, 1.167, 0, 1.533, 0, 0, 3.867, 0]}, {"Target": "Parameter", "Id": "ParamArmR", "Segments": [0, 0, 1, 0.144, 0, 0.289, 0, 0.433, 0, 1, 0.8, 0, 1.167, 0, 1.533, 0, 0, 3.867, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.144, 0, 0.289, 0, 0.433, 0, 1, 0.8, 0, 1.167, 0, 1.533, 0, 0, 3.867, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 0, 3.867, 0]}, {"Target": "Parameter", "Id": "ParamHairSide", "Segments": [0, 0, 0, 3.867, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 0, 3.867, 0]}]}