<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字人集成演示 - 鸿源技术学校AI助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            height: 100vh;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .container {
            display: flex;
            height: 95vh;
            max-width: 95%;
            width: 95%;
            margin: 0 auto;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }

        /* 左侧面板 */
        .left-panel {
            flex: 2;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px 0 0 20px;
            padding: 20px;
            display: flex;
            flex-direction: column;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            animation: slideInLeft 0.6s ease-out;
        }

        /* 中间聊天面板 */
        .chat-panel {
            flex: 3;
            display: flex;
            flex-direction: column;
            padding: 20px;
            background: #f8f9fa;
            min-width: 0;
            position: relative;
            animation: slideInUp 0.6s ease-out 0.2s both;
        }

        /* 右侧数字人面板 */
        .digital-human-panel {
            flex: 2;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            border-radius: 0 20px 20px 0;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            animation: slideInRight 0.6s ease-out 0.1s both;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        /* 数字人容器 */
        .digital-human-container {
            position: relative;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        /* 数字人头像 */
        .digital-human-avatar {
            width: 150px;
            height: 150px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 60px;
            animation: float 3s ease-in-out infinite;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        }

        .digital-human-avatar::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: shimmer 3s infinite;
        }

        /* 数字人信息 */
        .digital-human-info {
            text-align: center;
            color: #2c3e50;
        }

        .digital-human-name {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .digital-human-status {
            font-size: 14px;
            color: #28a745;
            margin-bottom: 15px;
        }

        .digital-human-features {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
        }

        .digital-human-features h4 {
            font-size: 14px;
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .digital-human-features ul {
            list-style: none;
            font-size: 12px;
            color: #666;
        }

        .digital-human-features li {
            margin: 5px 0;
            padding-left: 15px;
            position: relative;
        }

        .digital-human-features li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
        }

        /* 明学明事明德明志 艺术字样式 */
        .mingxue-art {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }

        .mingxue-char {
            font-family: 'STXingkai', 'KaiTi', 'FZShuTi', 'FZYaoti', '华文行楷', cursive, serif;
            font-size: 1.8rem;
            color: #764ba2;
            font-weight: bold;
            letter-spacing: 2px;
            text-shadow: 2px 2px 8px #e1e8ed, 0 2px 0 #fff, 0 0 8px #667eea44;
            transition: transform 0.2s;
        }

        .mingxue-char:hover {
            color: #1e3c72;
            transform: scale(1.08) rotate(-2deg);
        }

        /* 功能介绍 */
        .feature-intro {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 20px;
            color: white;
            font-size: 13px;
            line-height: 1.4;
            text-align: center;
        }

        /* 快捷按钮 */
        .quick-buttons {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-bottom: 20px;
        }

        .quick-btn {
            padding: 10px 15px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 600;
            transition: all 0.3s ease;
            color: white;
        }

        .quick-btn.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .quick-btn.success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .quick-btn.warning {
            background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%);
        }

        .quick-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        /* 用户信息 */
        .user-info {
            margin-top: auto;
        }

        .user-info h3 {
            color: #2c3e50;
            font-size: 14px;
            margin-bottom: 10px;
            text-align: center;
        }

        .info-input {
            width: 100%;
            padding: 8px 12px;
            margin-bottom: 8px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 12px;
            outline: none;
        }

        /* 聊天区域 */
        .chat-header {
            text-align: center;
            margin-bottom: 20px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            color: white;
        }

        .chat-messages {
            flex: 1;
            background: #ffffff;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            overflow-y: auto;
            border: 1px solid #e1e8ed;
        }

        .chat-input-container {
            display: flex;
            gap: 10px;
            background: rgba(255, 255, 255, 0.9);
            padding: 15px;
            border-radius: 16px;
        }

        .chat-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e1e8ed;
            border-radius: 20px;
            outline: none;
        }

        .send-button {
            padding: 12px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-weight: 600;
        }

        /* 动画 */
        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-30px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInRight {
            from { opacity: 0; transform: translateX(30px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                flex-direction: column;
                height: calc(100vh - 20px);
            }

            .left-panel {
                flex: none;
                height: auto;
                min-height: 180px;
                border-radius: 20px 20px 0 0;
                padding: 15px;
            }

            .chat-panel {
                flex: 1;
                border-radius: 0;
                padding: 15px;
            }

            .digital-human-panel {
                flex: none;
                height: 200px;
                border-radius: 0 0 20px 20px;
                padding: 15px;
            }

            .digital-human-avatar {
                width: 80px;
                height: 80px;
                font-size: 30px;
                margin-bottom: 10px;
            }

            .mingxue-char {
                font-size: 1.2rem;
            }

            .quick-buttons {
                flex-direction: row;
                gap: 5px;
            }

            .quick-btn {
                flex: 1;
                padding: 8px 10px;
                font-size: 11px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 左侧面板 -->
        <div class="left-panel">
            <!-- 明学明事明德明志 艺术字 -->
            <div class="mingxue-art">
                <span class="mingxue-char">明学</span>
                <span class="mingxue-char">明事</span>
                <span class="mingxue-char">明德</span>
                <span class="mingxue-char">明志</span>
            </div>

            <div class="feature-intro">
                🤖 我是您的专属AI助手，可以为您提供专业推荐、学校介绍等服务！
            </div>

            <!-- 快捷功能按钮 -->
            <div class="quick-buttons">
                <button class="quick-btn primary" onclick="insertText('专业推荐')">🎯 专业推荐</button>
                <button class="quick-btn success" onclick="insertText('学校介绍')">🏫 学校介绍</button>
                <button class="quick-btn warning" onclick="insertText('招生咨询')">📞 招生咨询</button>
            </div>

            <div class="user-info">
                <h3>用户信息</h3>
                <input type="text" class="info-input" placeholder="请输入您的姓名">
                <input type="text" class="info-input" placeholder="请输入您的手机号">
                <input type="text" class="info-input" placeholder="请输入您的身份证号">
            </div>
        </div>

        <!-- 中间聊天面板 -->
        <div class="chat-panel">
            <div class="chat-header">
                <h2>🏫 鸿源技术学校AI助手</h2>
                <p>培养技能人才，成就美好未来</p>
            </div>

            <div class="chat-messages" id="chatMessages">
                <div style="text-align: center; color: #666; padding: 20px;">
                    👋 您好！我是鸿源技术学校的AI助手，很高兴为您服务！<br>
                    右侧是我的数字人形象，您可以与我进行对话交流。
                </div>
            </div>

            <div class="chat-input-container">
                <input type="text" class="chat-input" id="chatInput" placeholder="请输入您的问题..." />
                <button class="send-button" onclick="sendMessage()">发送</button>
            </div>
        </div>

        <!-- 右侧数字人面板 -->
        <div class="digital-human-panel">
            <div class="digital-human-container">
                <div class="digital-human-avatar">🤖</div>
                <div class="digital-human-info">
                    <div class="digital-human-name">AI助手小鸿</div>
                    <div class="digital-human-status">● 在线服务中</div>
                </div>
                <div class="digital-human-features">
                    <h4>🌟 服务特色</h4>
                    <ul>
                        <li>智能专业推荐</li>
                        <li>实时在线咨询</li>
                        <li>个性化服务</li>
                        <li>24小时响应</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        function insertText(text) {
            const chatInput = document.getElementById('chatInput');
            chatInput.value = text;
            chatInput.focus();
        }

        function sendMessage() {
            const chatInput = document.getElementById('chatInput');
            const chatMessages = document.getElementById('chatMessages');
            const message = chatInput.value.trim();
            
            if (message) {
                // 添加用户消息
                const userMsg = document.createElement('div');
                userMsg.style.cssText = 'margin: 10px 0; padding: 12px; background: #667eea; color: white; border-radius: 10px; text-align: right; max-width: 80%; margin-left: auto;';
                userMsg.textContent = message;
                chatMessages.appendChild(userMsg);

                // 模拟AI回复
                setTimeout(() => {
                    const aiMsg = document.createElement('div');
                    aiMsg.style.cssText = 'margin: 10px 0; padding: 12px; background: #f0f0f0; border-radius: 10px; max-width: 80%; border: 1px solid #e0e0e0;';
                    
                    let response = '';
                    if (message.includes('专业推荐')) {
                        response = '🎯 根据当前就业市场分析，我为您推荐：<br>1. 计算机应用技术 - 就业前景广阔<br>2. 电子商务 - 发展潜力巨大<br>3. 汽车维修 - 技能实用稳定';
                    } else if (message.includes('学校介绍')) {
                        response = '🏫 鸿源技术学校秉承"明学明事明德明志"的校训，拥有现代化教学设施、专业师资团队，致力于培养技能人才，成就美好未来！';
                    } else if (message.includes('招生咨询')) {
                        response = '📞 招生咨询热线：13800138000<br>📧 邮箱：<EMAIL><br>🏢 地址：广东省珠海市香洲区<br>欢迎随时联系我们！';
                    } else {
                        response = '感谢您的咨询！我是鸿源技术学校的AI助手，可以为您提供专业推荐、学校介绍、招生咨询等服务。请点击左侧快捷按钮或直接提问。';
                    }
                    
                    aiMsg.innerHTML = response;
                    chatMessages.appendChild(aiMsg);
                    chatMessages.scrollTop = chatMessages.scrollHeight;
                }, 1000);

                chatInput.value = '';
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }
        }

        // 回车发送消息
        document.getElementById('chatInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // 页面加载完成后的欢迎消息
        window.addEventListener('load', function() {
            setTimeout(() => {
                const chatMessages = document.getElementById('chatMessages');
                const welcomeMsg = document.createElement('div');
                welcomeMsg.style.cssText = 'margin: 10px 0; padding: 12px; background: #e8f5e8; border-radius: 10px; border: 1px solid #28a745; text-align: center;';
                welcomeMsg.innerHTML = '🎉 数字人AI助手已就绪！您可以开始与我对话了。';
                chatMessages.appendChild(welcomeMsg);
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }, 2000);
        });
    </script>
</body>
</html>
