{"Version": 3, "Meta": {"PhysicsSettingCount": 9, "TotalInputCount": 32, "TotalOutputCount": 42, "VertexCount": 51, "EffectiveForces": {"Gravity": {"X": 0, "Y": -1}, "Wind": {"X": 0, "Y": 0}}, "PhysicsDictionary": [{"Id": "PhysicsSetting1", "Name": "後髪A"}, {"Id": "PhysicsSetting2", "Name": "後髪B"}, {"Id": "PhysicsSetting3", "Name": "後髪C"}, {"Id": "PhysicsSetting4", "Name": "右横髪"}, {"Id": "PhysicsSetting5", "Name": "左横髪"}, {"Id": "PhysicsSetting6", "Name": "前髪"}, {"Id": "PhysicsSetting7", "Name": "頭リボン"}, {"Id": "PhysicsSetting8", "Name": "胸リボン"}, {"Id": "PhysicsSetting9", "Name": "腰リボン"}]}, "PhysicsSettings": [{"Id": "PhysicsSetting1", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh82"}, "VertexIndex": 1, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh82"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh82"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh82"}, "VertexIndex": 4, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh82"}, "VertexIndex": 5, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh82"}, "VertexIndex": 6, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh82"}, "VertexIndex": 7, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 0.95, "Acceleration": 1.5, "Radius": 7}, {"Position": {"X": 0, "Y": 7}, "Mobility": 0.85, "Delay": 0.95, "Acceleration": 1.5, "Radius": 7}, {"Position": {"X": 0, "Y": 14}, "Mobility": 0.85, "Delay": 0.95, "Acceleration": 1.5, "Radius": 7}, {"Position": {"X": 0, "Y": 21}, "Mobility": 0.85, "Delay": 0.95, "Acceleration": 1.5, "Radius": 7}, {"Position": {"X": 0, "Y": 28}, "Mobility": 0.85, "Delay": 0.95, "Acceleration": 1.5, "Radius": 7}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.85, "Delay": 0.95, "Acceleration": 1.5, "Radius": 7}, {"Position": {"X": 0, "Y": 42}, "Mobility": 0.85, "Delay": 0.95, "Acceleration": 1.5, "Radius": 7}, {"Position": {"X": 0, "Y": 49}, "Mobility": 0.85, "Delay": 0.95, "Acceleration": 1.5, "Radius": 7}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting2", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh81"}, "VertexIndex": 1, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh81"}, "VertexIndex": 2, "Scale": 35, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh81"}, "VertexIndex": 3, "Scale": 35, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh81"}, "VertexIndex": 4, "Scale": 35, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh81"}, "VertexIndex": 5, "Scale": 35, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh81"}, "VertexIndex": 6, "Scale": 35, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh81"}, "VertexIndex": 7, "Scale": 35, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_8_ArtMesh81"}, "VertexIndex": 8, "Scale": 35, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_9_ArtMesh81"}, "VertexIndex": 9, "Scale": 35, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 0.9, "Acceleration": 1.5, "Radius": 6}, {"Position": {"X": 0, "Y": 6}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 1.5, "Radius": 6}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 1.5, "Radius": 6}, {"Position": {"X": 0, "Y": 18}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 1.5, "Radius": 6}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 2, "Radius": 6}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 2, "Radius": 6}, {"Position": {"X": 0, "Y": 36}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 2, "Radius": 6}, {"Position": {"X": 0, "Y": 42}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 2.5, "Radius": 6}, {"Position": {"X": 0, "Y": 48}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 2.5, "Radius": 6}, {"Position": {"X": 0, "Y": 54}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 2.5, "Radius": 6}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting3", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh80"}, "VertexIndex": 1, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh80"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh80"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh80"}, "VertexIndex": 4, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh80"}, "VertexIndex": 5, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh80"}, "VertexIndex": 6, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh80"}, "VertexIndex": 7, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_8_ArtMesh80"}, "VertexIndex": 8, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_9_ArtMesh80"}, "VertexIndex": 9, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 0.9, "Acceleration": 1.5, "Radius": 6}, {"Position": {"X": 0, "Y": 6}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 1.5, "Radius": 6}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 1.5, "Radius": 6}, {"Position": {"X": 0, "Y": 18}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 1.5, "Radius": 6}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 2, "Radius": 6}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 2, "Radius": 6}, {"Position": {"X": 0, "Y": 36}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 2, "Radius": 6}, {"Position": {"X": 0, "Y": 42}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 2.5, "Radius": 6}, {"Position": {"X": 0, "Y": 48}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 2.5, "Radius": 6}, {"Position": {"X": 0, "Y": 54}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 2.5, "Radius": 6}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting4", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh67"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh67"}, "VertexIndex": 2, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh67"}, "VertexIndex": 3, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh67"}, "VertexIndex": 4, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh67"}, "VertexIndex": 5, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 0.9, "Acceleration": 1.5, "Radius": 6}, {"Position": {"X": 0, "Y": 6}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 1.5, "Radius": 6}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 1.5, "Radius": 6}, {"Position": {"X": 0, "Y": 18}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 1.5, "Radius": 6}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 2, "Radius": 6}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 2, "Radius": 6}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting5", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh20"}, "VertexIndex": 1, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh20"}, "VertexIndex": 2, "Scale": 35, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh20"}, "VertexIndex": 3, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh20"}, "VertexIndex": 4, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh20"}, "VertexIndex": 5, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 0.9, "Acceleration": 1.5, "Radius": 6}, {"Position": {"X": 0, "Y": 5}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 1.5, "Radius": 6}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 1.5, "Radius": 6}, {"Position": {"X": 0, "Y": 15.5}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 1.5, "Radius": 6}, {"Position": {"X": 0, "Y": 21.5}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 2, "Radius": 6}, {"Position": {"X": 0, "Y": 27.5}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 2, "Radius": 6}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting6", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairFront01"}, "VertexIndex": 1, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairFront02"}, "VertexIndex": 2, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.8, "Delay": 0.9, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 7}, "Mobility": 0.8, "Delay": 0.9, "Acceleration": 1.5, "Radius": 7}, {"Position": {"X": 0, "Y": 14}, "Mobility": 0.8, "Delay": 0.9, "Acceleration": 1.5, "Radius": 7}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting7", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHeadRibbon"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 4}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1.5, "Radius": 4}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting8", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 100, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBustRibbon01"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamBustRibbon02"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 7}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1.2, "Radius": 7}, {"Position": {"X": 0, "Y": 13}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1.2, "Radius": 6}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting9", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 100, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamWaistRibbon01"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamWaistRibbon02"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.95, "Delay": 0.8, "Acceleration": 1.5, "Radius": 12}, {"Position": {"X": 0, "Y": 22}, "Mobility": 0.95, "Delay": 0.8, "Acceleration": 1.5, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}]}