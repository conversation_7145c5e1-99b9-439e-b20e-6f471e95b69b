{"Version": 3, "Meta": {"Duration": 3.333, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 25, "TotalSegmentCount": 109, "TotalPointCount": 278, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.333, 0, 0.667, 5, 1, 5, 1, 1.167, 5, 1.333, 5, 1.5, 5, 1, 1.833, 5, 2.167, -9, 2.5, -9, 0, 3.333, -9]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.167, 0, 0.333, 20, 0.5, 20, 1, 0.667, 20, 0.833, 10, 1, 10, 1, 1.167, 10, 1.333, 10, 1.5, 10, 1, 1.667, 10, 1.833, -12.5, 2, -12.5, 1, 2.167, -12.5, 2.333, 1, 2.5, 1, 0, 3.333, 1]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.5, 0, 1, 0, 1.5, 0, 1, 1.833, 0, 2.167, -9, 2.5, -9, 0, 3.333, -9]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.3, 1, 0.6, 1, 0.9, 1, 1, 0.956, 1, 1.011, 0, 1.067, 0, 1, 1.211, 0, 1.356, 1, 1.5, 1, 1, 1.611, 1, 1.722, 1, 1.833, 1, 1, 1.889, 1, 1.944, 0, 2, 0, 1, 2.144, 0, 2.289, 0.95, 2.433, 0.95, 0, 3.333, 0.95]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.3, 1, 0.6, 1, 0.9, 1, 1, 0.956, 1, 1.011, 0, 1.067, 0, 1, 1.211, 0, 1.356, 1, 1.5, 1, 1, 1.611, 1, 1.722, 1, 1.833, 1, 1, 1.889, 1, 1.944, 0, 2, 0, 1, 2.144, 0, 2.289, 0.96, 2.433, 0.96, 0, 3.333, 0.96]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.333, 0, 0.667, -0.09, 1, -0.09, 1, 1.167, -0.09, 1.333, -0.09, 1.5, -0.09, 1, 1.833, -0.09, 2.167, 0.4, 2.5, 0.4, 0, 3.333, 0.4]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 1, 0.167, 0, 0.333, -0.875, 0.5, -0.875, 1, 0.667, -0.875, 0.833, -0.33, 1, -0.33, 1, 1.167, -0.33, 1.333, -0.33, 1.5, -0.33, 1, 1.833, -0.33, 2.167, 0.21, 2.5, 0.21, 0, 3.333, 0.21]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0.32, 1, 0.32, 1, 1.167, 0.32, 1.333, 0.32, 1.5, 0.32, 1, 1.833, 0.32, 2.167, 0, 2.5, 0, 0, 3.333, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0.3, 1, 0.3, 1, 1.167, 0.3, 1.333, 0.3, 1.5, 0.3, 1, 1.833, 0.3, 2.167, 0, 2.5, 0, 0, 3.333, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 1, 0.5, 0, 1, 0, 1.5, 0, 0, 3.333, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 1, 0.5, 0, 1, 0, 1.5, 0, 0, 3.333, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.167, 0, 1.333, 0, 1.5, 0, 0, 3.333, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0.33, 1, 0.33, 1, 1.167, 0.33, 1.333, 0.33, 1.5, 0.33, 0, 3.333, 0.33]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 1, 0.333, 0, 0.667, 1, 1, 1, 1, 1.167, 1, 1.333, 1, 1.5, 1, 0, 3.333, 1]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 1, 0.333, 0, 0.667, 1, 1, 1, 1, 1.167, 1, 1.333, 1, 1.5, 1, 0, 3.333, 1]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 1, 0.5, 1, 1, 1, 1.5, 1, 0, 3.333, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 0, 0.067, 0, 0, 0.133, 0.008, 0, 0.267, 0.008, 1, 0.311, 0.006, 0.356, 0.205, 0.4, 0.204, 1, 0.422, 0.202, 0.444, 0.111, 0.467, 0.11, 1, 0.511, 0.107, 0.556, 0.45, 0.6, 0.447, 1, 0.622, 0.444, 0.644, 0.285, 0.667, 0.282, 1, 0.689, 0.287, 0.711, 0.568, 0.733, 0.573, 0, 0.8, 0.573, 1, 0.811, 0.566, 0.822, 0.709, 0.833, 0.831, 1, 0.844, 1.003, 0.856, 1.013, 0.867, 1, 0, 0.9, 1, 1, 0.922, 0.919, 0.944, 0.726, 0.967, 0.549, 1, 1, 0.271, 1.033, 0.014, 1.067, 0.008, 0, 1.133, 0.008, 0, 1.2, 0.016, 0, 1.267, 0.008, 0, 1.733, 0.008, 1, 1.778, 0.004, 1.822, 0.482, 1.867, 0.478, 1, 1.889, 0.472, 1.911, 0.085, 1.933, 0.078, 1, 1.978, 0.072, 2.022, 0.869, 2.067, 0.863, 1, 2.133, 0.869, 2.2, 0.041, 2.267, 0.047, 1, 2.289, 0.056, 2.311, 0.626, 2.333, 0.635, 1, 2.356, 0.63, 2.378, 0.296, 2.4, 0.29, 1, 2.422, 0.299, 2.444, 0.869, 2.467, 0.878, 1, 2.533, 0.885, 2.6, 0.001, 2.667, 0.008, 0, 2.8, 0.008, 0, 2.867, 0, 0, 3.267, 0, 0, 3.333, 0]}, {"Target": "Parameter", "Id": "ParamTere", "Segments": [0, 0, 1, 0.5, 0, 1, 0, 1.5, 0, 0, 3.333, 0]}, {"Target": "Parameter", "Id": "ParamArmR", "Segments": [0, 10, 1, 0.333, 10, 0.667, -10, 1, -10, 1, 1.167, -10, 1.333, -10, 1.5, -10, 0, 3.333, -10]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 10, 1, 0.333, 10, 0.667, -5, 1, -5, 1, 1.167, -5, 1.333, -5, 1.5, -5, 1, 1.833, -5, 2.167, -10, 2.5, -10, 0, 3.333, -10]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.5, 0, 1, 0, 1.5, 0, 1, 1.833, 0, 2.167, 6, 2.5, 6, 0, 3.333, 6]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.5, 0, 1, 0, 1.5, 0, 0, 3.333, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 0, 3.333, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 0, 3.333, 0]}, {"Target": "Parameter", "Id": "ParamRibbon", "Segments": [0, 0, 0, 3.333, 0]}]}