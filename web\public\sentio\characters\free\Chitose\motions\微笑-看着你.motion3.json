{"Version": 3, "Meta": {"Duration": 9.53, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 35, "TotalSegmentCount": 355, "TotalPointCount": 1024, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.589, 0, 1.011, 0, 1.433, 0, 1, 1.756, 0, 2.078, 0, 2.4, 0, 1, 2.667, 0, 2.933, 0, 3.2, 0, 1, 3.444, 0, 3.689, -1, 3.933, -1, 1, 4.056, -1, 4.178, 0, 4.3, 0, 1, 4.622, 0, 4.944, 0, 5.267, 0, 1, 5.378, 0, 5.489, 1, 5.6, 1, 1, 5.778, 1, 5.956, -9, 6.133, -9, 1, 6.344, -9, 6.556, 2, 6.767, 2, 1, 6.911, 2, 7.056, 0, 7.2, 0, 1, 7.522, 0, 7.844, 0, 8.167, 0, 0, 9.533, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.278, 0, 0.389, 7, 0.5, 7, 1, 0.678, 7, 0.856, -3, 1.033, -3, 1, 1.167, -3, 1.3, -3, 1.433, -3, 1, 1.756, -3, 2.078, -3, 2.4, -3, 1, 2.667, -3, 2.933, 15, 3.2, 15, 1, 3.444, 15, 3.689, -2, 3.933, -2, 1, 4.056, -2, 4.178, 0, 4.3, 0, 1, 4.622, 0, 4.944, 0, 5.267, 0, 1, 5.378, 0, 5.489, 3, 5.6, 3, 1, 5.778, 3, 5.956, -26, 6.133, -26, 1, 6.344, -26, 6.556, 3, 6.767, 3, 1, 6.911, 3, 7.056, 0, 7.2, 0, 1, 7.522, 0, 7.844, 0, 8.167, 0, 0, 9.533, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.278, 0, 0.389, 4, 0.5, 4, 1, 0.678, 4, 0.856, -18, 1.033, -18, 1, 1.167, -18, 1.3, -18, 1.433, -18, 1, 1.756, -18, 2.078, -18, 2.4, -18, 1, 2.667, -18, 2.933, -2.593, 3.2, 0, 1, 3.444, 2.377, 3.689, 2, 3.933, 2, 1, 4.056, 2, 4.178, 0, 4.3, 0, 1, 4.622, 0, 4.944, 0, 5.267, 0, 1, 5.378, 0, 5.489, 1, 5.6, 1, 1, 5.778, 1, 5.956, -11, 6.133, -11, 1, 6.344, -11, 6.556, 3, 6.767, 3, 1, 6.911, 3, 7.056, 0, 7.2, 0, 1, 7.522, 0, 7.844, 0, 8.167, 0, 0, 9.533, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.056, 1, 0.111, 1, 0.167, 1, 1, 0.589, 1, 1.011, 1, 1.433, 1, 1, 1.822, 1, 2.211, 1, 2.6, 1, 1, 2.644, 1, 2.689, 0, 2.733, 0, 1, 2.756, 0, 2.778, 0, 2.8, 0, 1, 2.856, 0, 2.911, 1, 2.967, 1, 1, 3.222, 1, 3.478, 1, 3.733, 1, 1, 3.778, 1, 3.822, 0, 3.867, 0, 1, 3.889, 0, 3.911, 0, 3.933, 0, 1, 3.989, 0, 4.044, 1, 4.1, 1, 1, 4.489, 1, 4.878, 1, 5.267, 1, 1, 5.633, 1, 6, 1, 6.367, 1, 1, 6.411, 1, 6.456, 0, 6.5, 0, 1, 6.522, 0, 6.544, 0, 6.567, 0, 1, 6.622, 0, 6.678, 1, 6.733, 1, 1, 6.744, 1, 6.756, 1, 6.767, 1, 1, 6.911, 1, 7.056, 1, 7.2, 1, 1, 7.522, 1, 7.844, 1, 8.167, 1, 0, 9.533, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.589, 0, 1.011, 0, 1.433, 0, 1, 2.711, 0, 3.989, 0, 5.267, 0, 1, 5.767, 0, 6.267, 0, 6.767, 0, 1, 6.911, 0, 7.056, 0, 7.2, 0, 1, 7.522, 0, 7.844, 0, 8.167, 0, 0, 9.533, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.056, 1, 0.111, 1, 0.167, 1, 1, 0.589, 1, 1.011, 1, 1.433, 1, 1, 1.822, 1, 2.211, 1, 2.6, 1, 1, 2.644, 1, 2.689, 0, 2.733, 0, 1, 2.756, 0, 2.778, 0, 2.8, 0, 1, 2.856, 0, 2.911, 1, 2.967, 1, 1, 3.222, 1, 3.478, 1, 3.733, 1, 1, 3.778, 1, 3.822, 0, 3.867, 0, 1, 3.889, 0, 3.911, 0, 3.933, 0, 1, 3.989, 0, 4.044, 1, 4.1, 1, 1, 4.489, 1, 4.878, 1, 5.267, 1, 1, 5.633, 1, 6, 1, 6.367, 1, 1, 6.411, 1, 6.456, 0, 6.5, 0, 1, 6.522, 0, 6.544, 0, 6.567, 0, 1, 6.622, 0, 6.678, 1, 6.733, 1, 1, 6.744, 1, 6.756, 1, 6.767, 1, 1, 6.911, 1, 7.056, 1, 7.2, 1, 1, 7.522, 1, 7.844, 1, 8.167, 1, 0, 9.533, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.589, 0, 1.011, 0, 1.433, 0, 1, 1.756, 0, 2.078, 0, 2.4, 0, 1, 3.033, 0, 3.667, 0, 4.3, 0, 1, 4.622, 0, 4.944, 0, 5.267, 0, 1, 5.767, 0, 6.267, 0, 6.767, 0, 1, 6.911, 0, 7.056, 0, 7.2, 0, 1, 7.522, 0, 7.844, 0, 8.167, 0, 0, 9.533, 0]}, {"Target": "Parameter", "Id": "ParamEyeForm", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.589, 0, 1.011, 0, 1.433, 0, 1, 1.756, 0, 2.078, 0, 2.4, 0, 1, 3.033, 0, 3.667, 0, 4.3, 0, 1, 4.622, 0, 4.944, 0, 5.267, 0, 1, 5.767, 0, 6.267, 0, 6.767, 0, 1, 6.911, 0, 7.056, 0, 7.2, 0, 1, 7.522, 0, 7.844, 0, 8.167, 0, 0, 9.533, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.456, 0, 0.744, 0, 1.033, 0, 1, 1.167, 0, 1.3, 0, 1.433, 0, 1, 1.756, 0, 2.078, 0, 2.4, 0, 1, 2.667, 0, 2.933, 0, 3.2, 0, 1, 3.444, 0, 3.689, 0.03, 3.933, 0.03, 1, 4.056, 0.03, 4.178, 0, 4.3, 0, 1, 4.622, 0, 4.944, 0, 5.267, 0, 1, 5.544, 0, 5.822, 0.22, 6.1, 0.22, 1, 6.322, 0.22, 6.544, -0.09, 6.767, -0.09, 1, 6.911, -0.09, 7.056, 0, 7.2, 0, 1, 7.522, 0, 7.844, 0, 8.167, 0, 0, 9.533, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.456, 0, 0.744, 0.15, 1.033, 0.15, 1, 1.167, 0.15, 1.3, 0.15, 1.433, 0.15, 1, 1.756, 0.15, 2.078, 0.15, 2.4, 0.15, 1, 2.667, 0.15, 2.933, -0.55, 3.2, -0.55, 1, 3.444, -0.55, 3.689, 0.1, 3.933, 0.1, 1, 4.056, 0.1, 4.178, 0, 4.3, 0, 1, 4.622, 0, 4.944, 0, 5.267, 0, 1, 5.544, 0, 5.822, 0.7, 6.1, 0.7, 1, 6.322, 0.7, 6.544, -0.1, 6.767, -0.1, 1, 6.911, -0.1, 7.056, 0, 7.2, 0, 1, 7.522, 0, 7.844, 0, 8.167, 0, 0, 9.533, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallForm", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.589, 0, 1.011, 0, 1.433, 0, 1, 1.756, 0, 2.078, 0, 2.4, 0, 1, 3.033, 0, 3.667, 0, 4.3, 0, 1, 4.622, 0, 4.944, 0, 5.267, 0, 1, 5.767, 0, 6.267, 0, 6.767, 0, 1, 6.911, 0, 7.056, 0, 7.2, 0, 1, 7.522, 0, 7.844, 0, 8.167, 0, 0, 9.533, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.589, 0, 1.011, 0, 1.433, 0, 1, 1.756, 0, 2.078, 0, 2.4, 0, 1, 3.033, 0, 3.667, 0, 4.3, 0, 1, 4.622, 0, 4.944, 0, 5.267, 0, 1, 5.767, 0, 6.267, 0, 6.767, 0, 1, 6.911, 0, 7.056, 0, 7.2, 0, 1, 7.522, 0, 7.844, 0, 8.167, 0, 0, 9.533, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.589, 0, 1.011, 0, 1.433, 0, 1, 1.756, 0, 2.078, 0, 2.4, 0, 1, 3.033, 0, 3.667, 0, 4.3, 0, 1, 4.622, 0, 4.944, 0, 5.267, 0, 1, 5.767, 0, 6.267, 0, 6.767, 0, 1, 6.911, 0, 7.056, 0, 7.2, 0, 1, 7.522, 0, 7.844, 0, 8.167, 0, 0, 9.533, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.589, 0, 1.011, 0, 1.433, 0, 1, 1.756, 0, 2.078, 0, 2.4, 0, 1, 3.033, 0, 3.667, 0, 4.3, 0, 1, 4.622, 0, 4.944, 0, 5.267, 0, 1, 5.767, 0, 6.267, 0, 6.767, 0, 1, 6.911, 0, 7.056, 0, 7.2, 0, 1, 7.522, 0, 7.844, 0, 8.167, 0, 0, 9.533, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.589, 0, 1.011, 0, 1.433, 0, 1, 1.756, 0, 2.078, 0, 2.4, 0, 1, 3.033, 0, 3.667, 0, 4.3, 0, 1, 4.622, 0, 4.944, 0, 5.267, 0, 1, 5.767, 0, 6.267, 0, 6.767, 0, 1, 6.911, 0, 7.056, 0, 7.2, 0, 1, 7.522, 0, 7.844, 0, 8.167, 0, 0, 9.533, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.589, 0, 1.011, 0, 1.433, 0, 1, 1.756, 0, 2.078, 0, 2.4, 0, 1, 3.033, 0, 3.667, 0, 4.3, 0, 1, 4.622, 0, 4.944, 0, 5.267, 0, 1, 5.767, 0, 6.267, 0, 6.767, 0, 1, 6.911, 0, 7.056, 0, 7.2, 0, 1, 7.522, 0, 7.844, 0, 8.167, 0, 0, 9.533, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.589, 0, 1.011, 0, 1.433, 0, 1, 1.756, 0, 2.078, 0, 2.4, 0, 1, 3.033, 0, 3.667, 0, 4.3, 0, 1, 4.622, 0, 4.944, 0, 5.267, 0, 1, 5.767, 0, 6.267, 0, 6.767, 0, 1, 6.911, 0, 7.056, 0, 7.2, 0, 1, 7.522, 0, 7.844, 0, 8.167, 0, 0, 9.533, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.589, 0, 1.011, 0, 1.433, 0, 1, 1.756, 0, 2.078, 0, 2.4, 0, 1, 3.033, 0, 3.667, 0, 4.3, 0, 1, 4.622, 0, 4.944, 0, 5.267, 0, 1, 5.767, 0, 6.267, 0, 6.767, 0, 1, 6.911, 0, 7.056, 0, 7.2, 0, 1, 7.522, 0, 7.844, 0, 8.167, 0, 0, 9.533, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.589, 0, 1.011, 0, 1.433, 0, 1, 1.756, 0, 2.078, 0, 2.4, 0, 1, 3.033, 0, 3.667, 0, 4.3, 0, 1, 4.622, 0, 4.944, 0, 5.267, 0, 1, 5.767, 0, 6.267, 0, 6.767, 0, 1, 6.911, 0, 7.056, 0, 7.2, 0, 1, 7.522, 0, 7.844, 0, 8.167, 0, 0, 9.533, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.589, 0, 1.011, 0, 1.433, 0, 1, 1.756, 0, 2.078, 0, 2.4, 0, 1, 3.033, 0, 3.667, 0, 4.3, 0, 1, 4.622, 0, 4.944, 0, 5.267, 0, 1, 5.767, 0, 6.267, 0, 6.767, 0, 1, 6.911, 0, 7.056, 0, 7.2, 0, 1, 7.522, 0, 7.844, 0, 8.167, 0, 0, 9.533, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.589, 0, 1.011, 0, 1.433, 0, 1, 1.756, 0, 2.078, 0, 2.4, 0, 1, 3.033, 0, 3.667, 0, 4.3, 0, 1, 4.622, 0, 4.944, 0, 5.267, 0, 1, 5.767, 0, 6.267, 0, 6.767, 0, 1, 6.911, 0, 7.056, 0, 7.2, 0, 1, 7.522, 0, 7.844, 0, 8.167, 0, 0, 9.533, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.589, 0, 1.011, 0, 1.433, 0, 1, 1.756, 0, 2.078, 0, 2.4, 0, 1, 3.033, 0, 3.667, 0, 4.3, 0, 1, 4.622, 0, 4.944, 0, 5.267, 0, 1, 5.767, 0, 6.267, 0, 6.767, 0, 1, 6.911, 0, 7.056, 0, 7.2, 0, 1, 7.522, 0, 7.844, 0, 8.167, 0, 0, 9.533, 0]}, {"Target": "Parameter", "Id": "ParamSweat", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.589, 0, 1.011, 0, 1.433, 0, 1, 1.756, 0, 2.078, 0, 2.4, 0, 1, 3.033, 0, 3.667, 0, 4.3, 0, 1, 4.622, 0, 4.944, 0, 5.267, 0, 1, 5.767, 0, 6.267, 0, 6.767, 0, 1, 6.911, 0, 7.056, 0, 7.2, 0, 1, 7.522, 0, 7.844, 0, 8.167, 0, 0, 9.533, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.456, 0, 0.744, -6, 1.033, -6, 1, 1.167, -6, 1.3, -6, 1.433, -6, 1, 1.756, -6, 2.078, -6, 2.4, -6, 1, 2.667, -6, 2.933, -3, 3.2, -3, 1, 3.567, -3, 3.933, -3, 4.3, -3, 1, 4.622, -3, 4.944, -3, 5.267, -3, 1, 5.378, -3, 5.489, 1, 5.6, 1, 1, 5.778, 1, 5.956, -2, 6.133, -2, 1, 6.344, -2, 6.556, 1, 6.767, 1, 1, 6.911, 1, 7.056, 0, 7.2, 0, 1, 7.522, 0, 7.844, 0, 8.167, 0, 0, 9.533, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.289, 0, 0.411, 1, 0.533, 1, 1, 0.7, 1, 0.867, -1, 1.033, -1, 1, 1.178, -1, 1.322, 0, 1.467, 0, 1, 1.844, 0, 2.222, 0, 2.6, 0, 1, 2.8, 0, 3, 4, 3.2, 4, 1, 3.278, 4, 3.356, 3.03, 3.433, 2, 1, 3.6, -0.208, 3.767, -1, 3.933, -1, 1, 4.056, -1, 4.178, 0, 4.3, 0, 1, 4.622, 0, 4.944, 0, 5.267, 0, 1, 5.556, 0, 5.844, -5, 6.133, -5, 1, 6.344, -5, 6.556, 1, 6.767, 1, 1, 6.911, 1, 7.056, 0, 7.2, 0, 1, 7.522, 0, 7.844, 0, 8.167, 0, 0, 9.533, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.289, 0, 0.411, 1, 0.533, 1, 1, 0.7, 1, 0.867, -5, 1.033, -5, 1, 1.222, -5, 1.411, -4.285, 1.6, -4.285, 1, 1.933, -4.285, 2.267, -4.285, 2.6, -4.285, 1, 2.8, -4.285, 3, 0, 3.2, 0, 1, 3.444, 0, 3.689, 0, 3.933, 0, 1, 4.056, 0, 4.178, 0, 4.3, 0, 1, 4.622, 0, 4.944, 0, 5.267, 0, 1, 5.556, 0, 5.844, -3, 6.133, -3, 1, 6.344, -3, 6.556, 1, 6.767, 1, 1, 6.911, 1, 7.056, 0, 7.2, 0, 1, 7.522, 0, 7.844, 0, 8.167, 0, 0, 9.533, 0]}, {"Target": "Parameter", "Id": "ParamArmLA", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.456, 0, 0.744, 1, 1.033, 1, 1, 1.133, 1, 1.233, 1, 1.333, 1, 1, 1.956, 1, 2.578, 0, 3.2, 0, 1, 3.567, 0, 3.933, 0, 4.3, 0, 1, 4.622, 0, 4.944, 0, 5.267, 0, 1, 5.556, 0, 5.844, 0, 6.133, 0, 1, 6.344, 0, 6.556, 1, 6.767, 1, 1, 6.956, 1, 7.144, 0, 7.333, 0, 1, 7.611, 0, 7.889, 0, 8.167, 0, 0, 9.533, 0]}, {"Target": "Parameter", "Id": "ParamArmRA", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.589, 0, 1.011, 0, 1.433, 0, 1, 1.756, 0, 2.078, 0, 2.4, 0, 1, 2.667, 0, 2.933, 0, 3.2, 0, 1, 3.567, 0, 3.933, 0, 4.3, 0, 1, 4.622, 0, 4.944, 0, 5.267, 0, 1, 5.556, 0, 5.844, 0, 6.133, 0, 1, 6.344, 0, 6.556, 0, 6.767, 0, 1, 6.911, 0, 7.056, 0, 7.2, 0, 1, 7.522, 0, 7.844, 0, 8.167, 0, 0, 9.533, 0]}, {"Target": "Parameter", "Id": "ParamArmRB", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.589, 0, 1.011, 0, 1.433, 0, 1, 1.756, 0, 2.078, 0, 2.4, 0, 1, 3.033, 0, 3.667, 0, 4.3, 0, 1, 4.622, 0, 4.944, 0, 5.267, 0, 1, 5.767, 0, 6.267, 0, 6.767, 0, 1, 6.911, 0, 7.056, 0, 7.2, 0, 1, 7.522, 0, 7.844, 0, 8.167, 0, 0, 9.533, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.589, 0, 1.011, 0, 1.433, 0, 1, 1.756, 0, 2.078, 0, 2.4, 0, 1, 3.033, 0, 3.667, 0, 4.3, 0, 1, 4.622, 0, 4.944, 0, 5.267, 0, 1, 5.767, 0, 6.267, 0, 6.767, 0, 1, 6.911, 0, 7.056, 0, 7.2, 0, 1, 7.522, 0, 7.844, 0, 8.167, 0, 0, 9.533, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.589, 0, 1.011, 0, 1.433, 0, 1, 1.756, 0, 2.078, 0, 2.4, 0, 1, 3.033, 0, 3.667, 0, 4.3, 0, 1, 4.622, 0, 4.944, 0, 5.267, 0, 1, 5.767, 0, 6.267, 0, 6.767, 0, 1, 6.911, 0, 7.056, 0, 7.2, 0, 1, 7.522, 0, 7.844, 0, 8.167, 0, 0, 9.533, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.589, 0, 1.011, 0, 1.433, 0, 1, 1.756, 0, 2.078, 0, 2.4, 0, 1, 3.033, 0, 3.667, 0, 4.3, 0, 1, 4.622, 0, 4.944, 0, 5.267, 0, 1, 5.767, 0, 6.267, 0, 6.767, 0, 1, 6.911, 0, 7.056, 0, 7.2, 0, 1, 7.522, 0, 7.844, 0, 8.167, 0, 0, 9.533, 0]}, {"Target": "PartOpacity", "Id": "PARTS_01_ARM_L_A", "Segments": [0, 1, 2, 8.87, 1, 0, 9.53, 1]}, {"Target": "PartOpacity", "Id": "PARTS_01_ARM_R_A", "Segments": [0, 1, 2, 8.87, 1, 0, 9.53, 1]}, {"Target": "PartOpacity", "Id": "PARTS_01_ARM_R_B", "Segments": [0, 0, 2, 8.87, 0, 0, 9.53, 0]}]}