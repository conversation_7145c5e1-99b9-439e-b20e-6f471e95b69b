{"Version": 3, "Meta": {"Duration": 4.133, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 29, "TotalSegmentCount": 188, "TotalPointCount": 505, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, -15, 1, 0.1, -15, 0.2, 0, 0.3, 0, 1, 0.478, 0, 0.656, -30, 0.833, -30, 1, 1.022, -30, 1.211, 0, 1.4, 0, 1, 1.589, 0, 1.778, -30, 1.967, -30, 1, 2.122, -30, 2.278, -15, 2.433, -15, 1, 2.611, -15, 2.789, -15, 2.967, -15, 0, 4.133, -15]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.1, 0, 0.2, 30, 0.3, 30, 1, 0.478, 30, 0.656, 24.557, 0.833, 18, 1, 1.022, 11.034, 1.211, 5.449, 1.4, 0, 1, 1.589, -5.449, 1.778, -8.357, 1.967, -15, 1, 2.122, -20.471, 2.278, -30, 2.433, -30, 1, 2.611, -30, 2.789, -30, 2.967, -30, 0, 4.133, -30]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.1, 0, 0.2, 30, 0.3, 30, 1, 0.478, 30, 0.656, -30, 0.833, -30, 1, 1.022, -30, 1.211, 30, 1.4, 30, 1, 1.589, 30, 1.778, -30, 1.967, -30, 1, 2.122, -30, 2.278, -30, 2.433, -30, 1, 2.611, -30, 2.789, 30, 2.967, 30, 0, 4.133, 30]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.1, 1.002, 0.2, 1.498, 0.3, 1.5, 1, 0.467, 1.5, 0.633, 1.46, 0.8, 1.353, 1, 0.878, 1.303, 0.956, 1.239, 1.033, 1.157, 1, 1.078, 1.111, 1.122, 1.058, 1.167, 1, 1, 1.211, 0.949, 1.256, -0.008, 1.3, 0, 0, 1.367, 0, 1, 1.378, -0.047, 1.389, 1.547, 1.4, 1.5, 1, 1.444, 1.503, 1.489, 1.222, 1.533, 1, 1, 1.578, 0.775, 1.622, 0.593, 1.667, 0.45, 1, 1.767, 0.127, 1.867, 0, 1.967, 0, 1, 2.144, -0.003, 2.322, 1.503, 2.5, 1.5, 0, 4.1, 1.5, 0, 4.133, 1.5]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 1, 0.656, 0, 1.311, 0, 1.967, 0, 1, 2.3, 0, 2.633, 0, 2.967, 0, 0, 4.133, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.1, 1.002, 0.2, 1.498, 0.3, 1.5, 1, 0.467, 1.5, 0.633, 1.46, 0.8, 1.353, 1, 0.878, 1.303, 0.956, 1.239, 1.033, 1.157, 1, 1.078, 1.111, 1.122, 1.058, 1.167, 1, 1, 1.211, 0.949, 1.256, -0.008, 1.3, 0, 0, 1.367, 0, 1, 1.378, -0.047, 1.389, 1.547, 1.4, 1.5, 1, 1.444, 1.503, 1.489, 1.222, 1.533, 1, 1, 1.578, 0.775, 1.622, 0.593, 1.667, 0.45, 1, 1.767, 0.127, 1.867, 0, 1.967, 0, 1, 2.144, -0.003, 2.322, 1.503, 2.5, 1.5, 0, 4.1, 1.5, 0, 4.133, 1.5]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 1, 0.656, 0, 1.311, 0, 1.967, 0, 1, 2.3, 0, 2.633, 0, 2.967, 0, 0, 4.133, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0.7, 1, 0.1, 0.7, 0.2, 0.7, 0.3, 0.7, 1, 0.856, 0.7, 1.411, 0.7, 1.967, 0.7, 1, 2.089, 0.7, 2.211, 0, 2.333, 0, 1, 2.422, 0, 2.511, 0, 2.6, 0, 1, 2.722, 0, 2.844, 0.7, 2.967, 0.7, 0, 4.133, 0.7]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 1, 0.1, 0, 0.2, 1, 0.3, 1, 1, 0.856, 1, 1.411, 0, 1.967, 0, 1, 2.089, 0, 2.211, 0, 2.333, 0, 1, 2.422, 0, 2.511, 0, 2.6, 0, 1, 2.722, 0, 2.844, 0, 2.967, 0, 0, 4.133, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallForm", "Segments": [0, 0, 1, 0.1, 0, 0.2, -1, 0.3, -1, 1, 0.667, -1, 1.033, -1, 1.4, -1, 1, 1.589, -1, 1.778, -1, 1.967, -1, 1, 2.178, -1, 2.389, 0, 2.6, 0, 1, 2.722, 0, 2.844, 0, 2.967, 0, 1, 3.078, 0, 3.189, 0, 3.3, 0, 0, 4.133, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 0.1, 0, 0.2, 1, 0.3, 1, 1, 0.667, 1, 1.033, 1, 1.4, 1, 1, 1.522, 1, 1.644, 1, 1.767, 1, 1, 1.833, 1, 1.9, 0, 1.967, 0, 1, 2.122, 0, 2.278, 0, 2.433, 0, 1, 2.489, 0, 2.544, 0, 2.6, 0, 1, 2.722, 0, 2.844, 0, 2.967, 0, 0, 4.133, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 0.1, 0, 0.2, 1, 0.3, 1, 1, 0.667, 1, 1.033, 1, 1.4, 1, 1, 1.522, 1, 1.644, 1, 1.767, 1, 1, 1.833, 1, 1.9, 0, 1.967, 0, 1, 2.122, 0, 2.278, 0, 2.433, 0, 1, 2.611, 0, 2.789, 0, 2.967, 0, 0, 4.133, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 0, 4.133, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 0, 4.133, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.667, 0, 1.033, 0, 1.4, 0, 1, 1.589, 0, 1.778, 0, 1.967, 0, 1, 2.3, 0, 2.633, 0, 2.967, 0, 0, 4.133, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 1, 0.656, 0, 1.311, 0, 1.967, 0, 1, 2.178, 0, 2.389, 0, 2.6, 0, 1, 2.722, 0, 2.844, 0, 2.967, 0, 0, 4.133, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.856, 0, 1.411, -1, 1.967, -1, 1, 2.178, -1, 2.389, -1, 2.6, -1, 1, 2.722, -1, 2.844, -1, 2.967, -1, 1, 3.078, -1, 3.189, -0.98, 3.3, -0.98, 0, 4.133, -0.98]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.856, 0, 1.411, -1, 1.967, -1, 1, 2.178, -1, 2.389, -1, 2.6, -1, 1, 2.722, -1, 2.844, -1, 2.967, -1, 1, 3.078, -1, 3.189, -1, 3.3, -1, 0, 4.133, -1]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 1, 0.1, 1, 0.2, -1, 0.3, -1, 1, 0.667, -1, 1.033, 0, 1.4, 0, 1, 1.589, 0, 1.778, -1, 1.967, -1, 1, 2.2, -1, 2.433, -1, 2.667, -1, 1, 2.767, -1, 2.867, -1, 2.967, -1, 1, 3.078, -1, 3.189, -1, 3.3, -1, 1, 3.333, -1, 3.367, -1, 3.4, -1, 0, 4.133, -1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.089, 0.002, 0.178, 0.796, 0.267, 1, 0, 1.5, 1, 1, 1.533, 0.905, 1.567, 0.882, 1.6, 0.861, 1, 1.622, 0.85, 1.644, 0.997, 1.667, 0.988, 1, 1.7, 0.851, 1.733, -0.104, 1.767, 0.051, 1, 1.8, 0.018, 1.833, 0.021, 1.867, 0.01, 1, 1.9, -0.055, 1.933, 0.139, 1.967, 1, 0, 2.033, 1, 2, 2.067, 0.996, 3, 2.1, 1, 0, 2.3, 1, 1, 2.311, 1.18, 2.322, 0.683, 2.333, 0.745, 1, 2.367, 0.759, 2.4, 0.692, 2.433, 1, 0, 2.467, 1, 2, 2.5, 0.91, 1, 2.533, 0.357, 2.567, 0.001, 2.6, 0.078, 1, 2.622, 0.083, 2.644, 0.328, 2.667, 0.4, 1, 2.689, 0.469, 2.711, 0.512, 2.733, 0.558, 1, 2.789, 0.663, 2.844, 0.79, 2.9, 0.988, 3, 2.933, 1, 0, 3.2, 1, 2, 3.233, 0.897, 1, 3.267, 0.271, 3.3, -0.008, 3.333, 0, 0, 4.1, 0, 0, 4.133, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, -5, 1, 0.178, -5, 0.356, 0, 0.533, 0, 1, 0.711, 0, 0.889, -5, 1.067, -5, 1, 1.256, -5, 1.444, 0, 1.633, 0, 1, 1.822, 0, 2.011, -5, 2.2, -5, 1, 2.356, -5, 2.511, -5, 2.667, -5, 1, 2.844, -5, 3.022, -5, 3.2, -5, 0, 4.133, -5]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.178, 0, 0.356, 10, 0.533, 10, 1, 0.711, 10, 0.889, 0.705, 1.067, -3, 1, 1.256, -6.937, 1.444, -7, 1.633, -7, 1, 1.822, -7, 2.011, 2, 2.2, 2, 1, 2.356, 2, 2.511, -4, 2.667, -4, 1, 2.844, -4, 3.022, 5, 3.2, 5, 1, 3.344, 5, 3.489, 0, 3.633, 0, 0, 4.133, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.178, 0, 0.356, 10, 0.533, 10, 1, 0.711, 10, 0.889, -10, 1.067, -10, 1, 1.256, -10, 1.444, 10, 1.633, 10, 1, 1.822, 10, 2.011, -10, 2.2, -10, 1, 2.356, -10, 2.511, -10, 2.667, -10, 1, 2.844, -10, 3.022, 10, 3.2, 10, 1, 3.344, 10, 3.489, 0, 3.633, 0, 0, 4.133, 0]}, {"Target": "Parameter", "Id": "ParamArmL", "Segments": [0, 0, 1, 0.1, 0, 0.2, -10, 0.3, -10, 1, 0.478, -10, 0.656, -10, 0.833, -10, 1, 1.022, -10, 1.211, -10, 1.4, -10, 1, 1.589, -10, 1.778, 10, 1.967, 10, 1, 2.122, 10, 2.278, -10, 2.433, -10, 1, 2.611, -10, 2.789, 0, 2.967, 0, 0, 4.133, 0]}, {"Target": "Parameter", "Id": "ParamArmR", "Segments": [0, 0, 1, 0.1, 0, 0.2, 10, 0.3, 10, 1, 0.478, 10, 0.656, -10, 0.833, -10, 1, 1.022, -10, 1.211, 10, 1.4, 10, 1, 1.589, 10, 1.778, -10, 1.967, -10, 1, 2.122, -10, 2.278, -10, 2.433, -10, 1, 2.611, -10, 2.789, 0, 2.967, 0, 0, 4.133, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.667, 0, 1.033, 0, 1.4, 0, 1, 1.589, 0, 1.778, 0, 1.967, 0, 1, 2.3, 0, 2.633, 0, 2.967, 0, 0, 4.133, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 0, 4.133, 0]}, {"Target": "Parameter", "Id": "ParamHairSide", "Segments": [0, 0, 0, 4.133, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 0, 4.133, 0]}]}