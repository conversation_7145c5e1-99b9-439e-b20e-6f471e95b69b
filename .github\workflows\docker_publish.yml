name: CI

on:
  push:
    branches:
      - main
      - feat/*
      - develop
    tags:
      - v*

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      - name: Set up QEMU for ARM emulation
        uses: docker/setup-qemu-action@v2
        with:
          platforms: linux/amd64,linux/arm64
      - name: Login to ALIYUN Docker Hub
        uses: docker/login-action@v1 
        with:
          username: ${{ secrets.ALIYUN_DOCKER_HUB_USERNAME }}
          password: ${{ secrets.ALIYUN_DOCKER_HUB_ACCESS_TOKEN }}
          registry: registry.cn-hangzhou.aliyuncs.com
      - name: Set Docker tag
        id: docker_tag
        run: |
          if [ "${{ github.event_name }}" == "push" ] && [[ "${{ github.ref }}" == refs/tags/* ]]; then
            # Tag push, use the tag name directly
            echo "tag=${{ github.ref_name }}" >> $GITHUB_ENV
          else
            # Branch push, use branch name and short commit ID
            if [ "${{ github.ref_name }}" == "main" ]; then
              echo "tag=main-latest" >> $GITHUB_ENV
            else
              branch=$(echo "${{ github.ref_name }}" | sed 's/\//-/g')
              short_sha=$(echo "${{ github.sha }}" | cut -c1-8)
              echo "tag=${branch}-${short_sha}" >> $GITHUB_ENV
            fi
          fi

      - name: Build and push adhweb docker image
        run: |
          docker buildx create --use
          docker buildx build --platform linux/amd64,linux/arm64 -t ${{secrets.ALIYUN_DOCKER_HUB_NAMESPACE}}/adh-web:${{ env.tag }} -f docker/adhWeb.Dockerfile . --push
      - name: Build and push adhserver docker image
        run: |
          docker buildx create --use
          docker buildx build --platform linux/amd64,linux/arm64 -t ${{secrets.ALIYUN_DOCKER_HUB_NAMESPACE}}/adh-api:${{ env.tag }} -f docker/adhServer.Dockerfile . --push


          

          
