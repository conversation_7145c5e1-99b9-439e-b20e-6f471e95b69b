{"Version": 3, "Meta": {"PhysicsSettingCount": 11, "TotalInputCount": 34, "TotalOutputCount": 35, "VertexCount": 58, "EffectiveForces": {"Gravity": {"X": 0, "Y": -1}, "Wind": {"X": 0, "Y": 0}}, "PhysicsDictionary": [{"Id": "PhysicsSetting1", "Name": "前髪"}, {"Id": "PhysicsSetting2", "Name": "後ろ髪"}, {"Id": "PhysicsSetting3", "Name": "リボン　髪"}, {"Id": "PhysicsSetting4", "Name": "スカート　横揺れ"}, {"Id": "PhysicsSetting5", "Name": "スカート　縦揺れ"}, {"Id": "PhysicsSetting6", "Name": "リボン　体"}, {"Id": "PhysicsSetting7", "Name": "胸揺れ"}, {"Id": "PhysicsSetting8", "Name": "サイドアップ　右"}, {"Id": "PhysicsSetting9", "Name": "サイドアップ　左"}, {"Id": "PhysicsSetting10", "Name": "横髪　右"}, {"Id": "PhysicsSetting11", "Name": "横髪　左"}]}, "PhysicsSettings": [{"Id": "PhysicsSetting1", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairFront"}, "VertexIndex": 1, "Scale": 1.522, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 3}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 1.5, "Radius": 3}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting2", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairBack"}, "VertexIndex": 1, "Scale": 2.061, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.95, "Delay": 0.8, "Acceleration": 1.5, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -30, "Default": 0, "Maximum": 30}}}, {"Id": "PhysicsSetting3", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamSideupRibbon"}, "VertexIndex": 1, "Scale": 1.775, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.6, "Acceleration": 1.5, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -7.8, "Default": 0, "Maximum": 8}}}, {"Id": "PhysicsSetting4", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 100, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamSkirt"}, "VertexIndex": 1, "Scale": 1.434, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.6, "Acceleration": 1.5, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting5", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamSkirt2"}, "VertexIndex": 1, "Scale": 1.306, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.8, "Delay": 0.9, "Acceleration": 2, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting6", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 100, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamRibbon"}, "VertexIndex": 1, "Scale": 1.402, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.6, "Acceleration": 1.5, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10.6}}}, {"Id": "PhysicsSetting7", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBustY"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 3}, "Mobility": 0.8, "Delay": 0.9, "Acceleration": 1.5, "Radius": 3}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting8", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh61"}, "VertexIndex": 1, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh61"}, "VertexIndex": 2, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh61"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh61"}, "VertexIndex": 4, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh61"}, "VertexIndex": 5, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh61"}, "VertexIndex": 6, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh61"}, "VertexIndex": 7, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 10}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 10}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 10}, {"Position": {"X": 0, "Y": 70}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 10}, {"Position": {"X": 0, "Y": 80}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 10}, {"Position": {"X": 0, "Y": 90}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 10}, {"Position": {"X": 0, "Y": 100}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting9", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh62"}, "VertexIndex": 1, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh62"}, "VertexIndex": 2, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh62"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh62"}, "VertexIndex": 4, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh62"}, "VertexIndex": 5, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh62"}, "VertexIndex": 6, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh62"}, "VertexIndex": 7, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 10}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 10}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 10}, {"Position": {"X": 0, "Y": 70}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 10}, {"Position": {"X": 0, "Y": 80}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 10}, {"Position": {"X": 0, "Y": 90}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 10}, {"Position": {"X": 0, "Y": 100}, "Mobility": 0.85, "Delay": 0.85, "Acceleration": 1.2, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting10", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh54"}, "VertexIndex": 1, "Scale": 5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh54"}, "VertexIndex": 2, "Scale": 5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh54"}, "VertexIndex": 3, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh54"}, "VertexIndex": 4, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh54"}, "VertexIndex": 5, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh54"}, "VertexIndex": 6, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh54"}, "VertexIndex": 7, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 1.2, "Radius": 15}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 1.2, "Radius": 15}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 1.2, "Radius": 15}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 1.2, "Radius": 15}, {"Position": {"X": 0, "Y": 75}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 1.2, "Radius": 15}, {"Position": {"X": 0, "Y": 90}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 1.2, "Radius": 15}, {"Position": {"X": 0, "Y": 105}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 1.2, "Radius": 15}, {"Position": {"X": 0, "Y": 120}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 1.2, "Radius": 15}, {"Position": {"X": 0, "Y": 135}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 1.2, "Radius": 15}, {"Position": {"X": 0, "Y": 150}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 1.2, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting11", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh55"}, "VertexIndex": 1, "Scale": 5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh55"}, "VertexIndex": 2, "Scale": 5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh55"}, "VertexIndex": 3, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh55"}, "VertexIndex": 4, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh55"}, "VertexIndex": 5, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh55"}, "VertexIndex": 6, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh55"}, "VertexIndex": 7, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 1.2, "Radius": 15}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 1.2, "Radius": 15}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 1.2, "Radius": 15}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 1.2, "Radius": 15}, {"Position": {"X": 0, "Y": 75}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 1.2, "Radius": 15}, {"Position": {"X": 0, "Y": 90}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 1.2, "Radius": 15}, {"Position": {"X": 0, "Y": 105}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 1.2, "Radius": 15}, {"Position": {"X": 0, "Y": 120}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 1.2, "Radius": 15}, {"Position": {"X": 0, "Y": 135}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 1.2, "Radius": 15}, {"Position": {"X": 0, "Y": 150}, "Mobility": 0.9, "Delay": 0.85, "Acceleration": 1.2, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}]}