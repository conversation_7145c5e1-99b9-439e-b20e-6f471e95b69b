{"Version": 3, "Meta": {"Duration": 8, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 54, "TotalSegmentCount": 303, "TotalPointCount": 793, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.1, 0, 0.2, 30, 0.3, 30, 1, 0.433, 30, 0.567, -30, 0.7, -30, 1, 0.978, -30, 1.256, -30, 1.533, -30, 1, 2.022, -30, 2.511, 30, 3, 30, 1, 3.8, 30, 4.6, 30, 5.4, 30, 1, 5.667, 30, 5.933, 0, 6.2, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.189, 0, 0.378, 3.748, 0.567, 10, 1, 0.678, 13.678, 0.789, 15, 0.9, 15, 1, 1.033, 15, 1.167, 10, 1.3, 10, 1, 1.411, 10, 1.522, 10, 1.633, 10, 1, 2.1, 10, 2.567, 0, 3.033, 0, 1, 3.167, 0, 3.3, 0, 3.433, 0, 1, 3.489, 0, 3.544, -13, 3.6, -13, 1, 3.744, -13, 3.889, 30, 4.033, 30, 1, 4.189, 30, 4.344, 12, 4.5, 12, 1, 4.8, 12, 5.1, 12, 5.4, 12, 1, 5.522, 12, 5.644, 12, 5.767, 12, 1, 5.911, 12, 6.056, 0, 6.2, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 2.033, 1, 4.067, 1, 6.1, 1, 0, 8, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 2.033, 1, 4.067, 1, 6.1, 1, 0, 8, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.1, 0, 0.2, -0.6, 0.3, -0.6, 1, 0.433, -0.6, 0.567, 0, 0.7, 0, 1, 1.644, 0, 2.589, 0, 3.533, 0, 1, 3.611, 0, 3.689, -0.2, 3.767, -0.2, 1, 4.011, -0.2, 4.256, 0, 4.5, 0, 1, 5.033, 0, 5.567, 0, 6.1, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 1, 1.178, 0, 2.356, 0, 3.533, 0, 1, 3.611, 0, 3.689, -0.8, 3.767, -0.8, 1, 3.856, -0.8, 3.944, -0.8, 4.033, -0.8, 1, 4.189, -0.8, 4.344, 0, 4.5, 0, 1, 5.033, 0, 5.567, 0, 6.1, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 6.925, 0.167, 9, 1, 0.2, 10.245, 0.233, 10, 0.267, 10, 1, 0.411, 10, 0.556, -10, 0.7, -10, 1, 0.989, -10, 1.278, -10, 1.567, -10, 1, 2.044, -10, 2.522, 0, 3, 0, 1, 3.189, 0, 3.378, 0, 3.567, 0, 1, 3.611, 0, 3.656, 10, 3.7, 10, 1, 3.922, 10, 4.144, 8, 4.367, 8, 1, 4.689, 8, 5.011, 8, 5.333, 8, 1, 5.589, 8, 5.844, 0, 6.1, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.522, 0, 1.044, 0, 1.567, 0, 1, 2.822, 0, 4.078, 0, 5.333, 0, 1, 5.589, 0, 5.844, 0, 6.1, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.1, 0, 0.2, -3, 0.3, -3, 1, 0.444, -3, 0.589, 1, 0.733, 1, 1, 0.933, 1, 1.133, 0, 1.333, 0, 1, 1.422, 0, 1.511, 0, 1.6, 0, 1, 2.067, 0, 2.533, -3, 3, -3, 1, 3.122, -3, 3.244, -3, 3.367, -3, 1, 3.433, -3, 3.5, -5, 3.567, -5, 1, 3.6, -5, 3.633, 4.75, 3.667, 6, 1, 3.756, 9.333, 3.844, 10, 3.933, 10, 1, 4.089, 10, 4.244, 1, 4.4, 1, 1, 4.522, 1, 4.644, 2, 4.767, 2, 1, 4.956, 2, 5.144, 2, 5.333, 2, 1, 5.589, 2, 5.844, 0, 6.1, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamShoulderR", "Segments": [0, 0, 1, 1.189, 0, 2.378, 0, 3.567, 0, 1, 3.611, 0, 3.656, 7.767, 3.7, 8.4, 1, 3.8, 9.824, 3.9, 10, 4, 10, 1, 4.133, 10, 4.267, 0, 4.4, 0, 1, 4.711, 0, 5.022, 0, 5.333, 0, 1, 5.589, 0, 5.844, 0, 6.1, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamShoulderL", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0, 0.267, 0, 1, 1.956, 0, 3.644, 0, 5.333, 0, 1, 5.589, 0, 5.844, 0, 6.1, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamLegKnee", "Segments": [0, 0, 1, 0.522, 0, 1.044, 0, 1.567, 0, 1, 2.167, 0, 2.767, 0, 3.367, 0, 1, 3.433, 0, 3.5, -2, 3.567, -2, 1, 3.622, -2, 3.678, 5.887, 3.733, 7, 1, 3.789, 8.113, 3.844, 8, 3.9, 8, 1, 4.044, 8, 4.189, 0, 4.333, 0, 1, 4.667, 0, 5, 0, 5.333, 0, 1, 5.589, 0, 5.844, 0, 6.1, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamLegR", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0, 0.267, 0, 1, 0.422, 0, 0.578, -5, 0.733, -5, 1, 1.678, -5, 2.622, -5, 3.567, -5, 1, 3.689, -5, 3.811, 0, 3.933, 0, 1, 4.1, 0, 4.267, -4, 4.433, -4, 1, 4.733, -4, 5.033, -4, 5.333, -4, 1, 5.589, -4, 5.844, 0, 6.1, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamLegRUpDw", "Segments": [0, 0, 1, 1.189, 0, 2.378, 0, 3.567, 0, 1, 3.678, 0, 3.789, 10, 3.9, 10, 1, 4.078, 10, 4.256, 0, 4.433, 0, 1, 4.733, 0, 5.033, 0, 5.333, 0, 1, 5.589, 0, 5.844, 0, 6.1, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamAllZ", "Segments": [0, 0, 1, 1.189, 0, 2.378, 0, 3.567, 0, 1, 3.633, 0, 3.7, 19.528, 3.767, 21, 1, 3.822, 22.226, 3.878, 22, 3.933, 22, 1, 4.067, 22, 4.2, -2, 4.333, -2, 1, 4.922, -2, 5.511, 0, 6.1, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamArmR01", "Segments": [0, 0, 1, 0.044, 0, 0.089, -18, 0.133, -18, 1, 0.2, -20.878, 0.267, -21, 0.333, -21, 1, 0.433, -21, 0.533, 29, 0.633, 29, 1, 0.956, 29, 1.278, 29, 1.6, 29, 1, 2.056, 29, 2.511, 30, 2.967, 30, 1, 3.089, 30, 3.211, 30, 3.333, 30, 1, 3.389, 30, 3.444, 30, 3.5, 30, 1, 3.533, 30, 3.567, 37.934, 3.6, 39, 1, 3.744, 43.62, 3.889, 45, 4.033, 45, 1, 4.156, 45, 4.278, 29, 4.4, 29, 1, 4.533, 29, 4.667, 30, 4.8, 30, 1, 4.978, 30, 5.156, 30, 5.333, 30, 1, 5.589, 30, 5.844, -0.525, 6.1, -0.525, 1, 6.267, -0.525, 6.433, 0, 6.6, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamArmR01Y", "Segments": [0, 0, 1, 0.111, 0, 0.222, -7.2, 0.333, -7.2, 1, 0.456, -7.2, 0.578, 0, 0.7, 0, 1, 1, 0, 1.3, 0, 1.6, 0, 1, 2.056, 0, 2.511, -10, 2.967, -10, 1, 3.144, -10, 3.322, -10, 3.5, -10, 1, 4.111, -10, 4.722, -10, 5.333, -10, 1, 5.589, -10, 5.844, 0, 6.1, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamArmR02", "Segments": [0, 0, 1, 0.089, 0, 0.178, -41, 0.267, -41, 1, 0.367, -41, 0.467, -10.206, 0.567, -3, 1, 0.622, 1.003, 0.678, 0, 0.733, 0, 1, 0.822, 0, 0.911, 0, 1, 0, 1, 1.2, 0, 1.4, 0, 1.6, 0, 1, 2.067, 0, 2.533, 3, 3, 3, 1, 3.178, 3, 3.356, 3, 3.533, 3, 1, 3.567, 3, 3.6, 3, 3.633, 3, 1, 3.711, 3, 3.789, 6, 3.867, 6, 1, 4.067, 6, 4.267, 0, 4.467, 0, 1, 4.756, 0, 5.044, 0, 5.333, 0, 1, 5.589, 0, 5.844, 0, 6.1, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamArmR02Y", "Segments": [0, -10, 1, 0.033, -10, 0.067, -20, 0.1, -20, 1, 0.189, -20, 0.278, -20, 0.367, -20, 1, 0.411, -20, 0.456, 10, 0.5, 10, 1, 0.544, 10, 0.589, -9, 0.633, -9, 1, 0.956, -9, 1.278, -9, 1.6, -9, 1, 2.033, -9, 2.467, 8, 2.9, 8, 1, 3.711, 8, 4.522, 8, 5.333, 8, 1, 5.589, 8, 5.844, -10, 6.1, -10, 0, 8, -10]}, {"Target": "Parameter", "Id": "ParamArmR03", "Segments": [0, 0, 1, 0.133, 0, 0.267, -21, 0.4, -21, 1, 0.522, -21, 0.644, 4, 0.767, 4, 1, 0.856, 4, 0.944, 0, 1.033, 0, 1, 1.833, 0, 2.633, 0, 3.433, 0, 1, 3.478, 0, 3.522, -21, 3.567, -21, 1, 3.611, -21, 3.656, 16, 3.7, 16, 1, 3.856, 16, 4.011, 16, 4.167, 16, 1, 4.311, 16, 4.456, -2, 4.6, -2, 1, 4.711, -2, 4.822, 0, 4.933, 0, 1, 5.067, 0, 5.2, 0, 5.333, 0, 1, 5.589, 0, 5.844, 0, 6.1, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamArmL01", "Segments": [0, 0, 1, 1.778, 0, 3.556, 0, 5.333, 0, 1, 5.589, 0, 5.844, 0, 6.1, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamArmL02", "Segments": [0, 0, 1, 1.189, 0, 2.378, -8, 3.567, -8, 1, 3.689, -8, 3.811, 11, 3.933, 11, 1, 4.056, 11, 4.178, -3, 4.3, -3, 1, 4.444, -3, 4.589, 0, 4.733, 0, 1, 4.933, 0, 5.133, 0, 5.333, 0, 1, 5.589, 0, 5.844, 0, 6.1, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamArmL03", "Segments": [0, 0, 1, 1.778, 0, 3.556, 0, 5.333, 0, 1, 5.589, 0, 5.844, 0, 6.1, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamArmChange", "FadeInTime": 0.0, "FadeOutTime": 0.0, "Segments": [0, 0, 1, 0.178, 0, 0.356, 0, 0.533, 0, 2, 0.567, 1, 1, 2.289, 1, 4.011, 1, 5.733, 1, 2, 5.767, 0, 2, 8, 0]}, {"Target": "Parameter", "Id": "ParamBookPage", "Segments": [0, 0, 0, 3.633, 0, 0, 3.833, 30, 2, 3.867, 0, 0, 4.067, 30, 2, 4.1, 0, 0, 4.333, 30, 2, 4.367, 0, 0, 4.733, 30, 2, 4.767, 0, 0, 5.367, 30, 2, 5.4, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamFlameOn", "Segments": [0, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamFlame", "Segments": [0, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamFlameShaking", "Segments": [0, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamFlameX", "Segments": [0, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamFlameY", "Segments": [0, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamCharge01On", "Segments": [0, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamCharge01", "Segments": [0, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamHandLightAOn", "Segments": [0, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamHandLightASize", "Segments": [0, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamMagicPowersA", "Segments": [0, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamMagicAOn", "Segments": [0, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamMagicARotation", "Segments": [0, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamMagicALight", "Segments": [0, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamEffectAX", "Segments": [0, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamEffectAY", "Segments": [0, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamHandLightBOn", "Segments": [0, 0, 1, 0.233, 0, 0.467, 0, 0.7, 0, 1, 0.767, 0, 0.833, 1, 0.9, 1, 2, 3.533, 1, 2, 3.567, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamHandLightBSize", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.822, 0, 0.978, 0.6, 1.133, 0.6, 1, 1.222, 0.6, 1.311, 0.4, 1.4, 0.4, 1, 1.467, 0.4, 1.533, 0.6, 1.6, 0.6, 1, 1.667, 0.6, 1.733, 0.4, 1.8, 0.4, 1, 1.867, 0.4, 1.933, 0.6, 2, 0.6, 1, 2.067, 0.6, 2.133, 0.4, 2.2, 0.4, 1, 2.267, 0.4, 2.333, 0.6, 2.4, 0.6, 1, 2.467, 0.6, 2.533, 0.4, 2.6, 0.4, 1, 2.667, 0.4, 2.733, 0.6, 2.8, 0.6, 1, 2.911, 0.6, 3.022, 0.4, 3.133, 0.4, 1, 3.222, 0.4, 3.311, 1, 3.4, 1, 1, 3.467, 1, 3.533, 0, 3.6, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamMagicBOn", "Segments": [0, 0, 0, 0.633, 0, 0, 1.367, 6, 1, 2.1, 6, 2.833, 6, 3.567, 6, 1, 3.667, 6, 3.767, 0, 3.867, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamMagicBRotation", "Segments": [0, 0, 0, 8, 360]}, {"Target": "Parameter", "Id": "ParamMagicBMove", "Segments": [0, 0, 1, 0.256, 0, 0.511, 0, 0.767, 0, 1, 1.022, 0, 1.278, 1, 1.533, 1, 1, 2.211, 1, 2.889, 1, 3.567, 1, 1, 3.711, 1, 3.856, 0, 4, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamMagicBX", "Segments": [0, -10, 1, 0.533, -10, 1.067, -10, 1.6, -10, 1, 2.056, -10, 2.511, 8, 2.967, 8, 0, 8, 8]}, {"Target": "Parameter", "Id": "ParamMagicPowersBOn", "Segments": [0, 0, 1, 1.178, 0, 2.356, 0, 3.533, 0, 1, 3.544, 0, 3.556, 1, 3.567, 1, 1, 3.744, 1, 3.922, 1, 4.1, 1, 1, 4.133, 1, 4.167, 0, 4.2, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamMagicPowersBSize", "Segments": [0, 0, 1, 1.178, 0, 2.356, 0, 3.533, 0, 1, 3.556, 0, 3.578, 0.553, 3.6, 0.6, 1, 3.744, 0.903, 3.889, 1, 4.033, 1, 1, 4.089, 1, 4.144, 0, 4.2, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamMagicPowersBThicknesses", "Segments": [0, 0, 1, 1.167, 0, 2.333, 0, 3.5, 0, 1, 3.522, 0, 3.544, 10, 3.567, 10, 1, 3.589, 10, 3.611, 2.2, 3.633, 2.2, 1, 3.656, 2.2, 3.678, 7.987, 3.7, 8.5, 1, 3.756, 9.783, 3.811, 10, 3.867, 10, 1, 3.944, 10, 4.022, 0, 4.1, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamEffectBX", "Segments": [0, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamEffectBY", "Segments": [0, 0, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamSkirt", "Segments": [0, 0, 0, 1, 30, 2, 1.033, 1, 0, 2.033, 30, 2, 2.067, 1, 0, 2.967, 30, 2, 3, 1, 0, 3.833, 30, 2, 3.867, 1, 0, 4.533, 30, 2, 4.567, 1.875, 0, 5.067, 30, 2, 5.1, 1.875, 0, 5.767, 30, 2, 5.8, 1.875, 0, 6.833, 30, 2, 6.867, 1, 0, 8, 30]}, {"Target": "Parameter", "Id": "ParamSkirtX", "Segments": [0, 0, 1, 0.156, 0, 0.311, 1, 0.467, 1, 1, 0.667, 1, 0.867, -1, 1.067, -1, 1, 1.311, -1, 1.556, 0.6, 1.8, 0.6, 1, 2.056, 0.6, 2.311, -0.5, 2.567, -0.5, 1, 2.756, -0.5, 2.944, 0.4, 3.133, 0.4, 1, 3.267, 0.4, 3.4, -0.4, 3.533, -0.4, 1, 3.678, -0.4, 3.822, 1, 3.967, 1, 1, 4.089, 1, 4.211, 0, 4.333, 0, 1, 4.511, 0, 4.689, 1, 4.867, 1, 1, 5, 1, 5.133, 0.1, 5.267, 0.1, 1, 5.433, 0.1, 5.6, 1, 5.767, 1, 1, 5.989, 1, 6.211, -0.6, 6.433, -0.6, 1, 6.678, -0.6, 6.922, 0.6, 7.167, 0.6, 1, 7.444, 0.6, 7.722, 0, 8, 0]}, {"Target": "Parameter", "Id": "ParamSkirtY", "Segments": [0, 0, 1, 0.289, 0, 0.578, 0.4, 0.867, 0.4, 1, 1.189, 0.4, 1.511, -0.7, 1.833, -0.7, 1, 2.144, -0.7, 2.456, 0.4, 2.767, 0.4, 1, 2.978, 0.4, 3.189, -0.5, 3.4, -0.5, 1, 3.522, -0.5, 3.644, 1, 3.767, 1, 1, 3.867, 1, 3.967, -1, 4.067, -1, 1, 4.2, -1, 4.333, 0.7, 4.467, 0.7, 1, 4.611, 0.7, 4.756, -0.6, 4.9, -0.6, 1, 5.122, -0.6, 5.344, 0.2, 5.567, 0.2, 1, 5.756, 0.2, 5.944, -0.6, 6.133, -0.6, 1, 6.411, -0.6, 6.689, 0.1, 6.967, 0.1, 1, 7.311, 0.1, 7.656, 0, 8, 0]}]}