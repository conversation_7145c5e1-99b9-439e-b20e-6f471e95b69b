# 数字人集成页面说明

## 概述

本项目成功将原有的index.html页面与数字人功能进行了集成，创建了一个全新的交互式AI助手页面。数字人显示在页面右侧，提供更加生动的用户体验。

## 文件结构

```
├── index.html                      # 原始页面（已添加数字人容器）
├── index_with_digital_human.html   # 完整的数字人集成页面
├── start_digital_human.html        # 启动器页面
└── 数字人集成说明.md               # 本说明文件
```

## 主要功能

### 🎯 核心功能
- **智能专业推荐**：基于用户需求提供个性化专业建议
- **实时聊天对话**：支持自然语言交互
- **Live2D数字人**：右侧显示动态数字人形象
- **学校信息介绍**：详细的学校特色和办学理念
- **招生咨询服务**：完整的联系方式和咨询信息

### 🎨 界面设计
- **三栏布局**：左侧用户信息 + 中间聊天区域 + 右侧数字人
- **响应式设计**：完美适配PC端和移动端
- **现代化UI**：渐变背景、圆角设计、动画效果
- **快捷操作**：左侧提供快捷功能按钮

## 使用方法

### 1. 快速启动
```bash
# 直接在浏览器中打开启动器
open start_digital_human.html
```

### 2. 功能使用
1. **专业推荐**：点击左侧"专业推荐"按钮或输入相关关键词
2. **学校介绍**：点击"学校介绍"按钮了解学校详情
3. **招生咨询**：点击"招生咨询"获取联系方式
4. **自由对话**：在聊天框中输入任何问题

### 3. 数字人交互
- 数字人显示在页面右侧
- 支持Live2D动画效果
- 如果数字人库未加载，会显示占位符图标
- 可以与数字人进行语音和文字交互

## 技术特点

### 🔧 技术栈
- **前端框架**：原生HTML/CSS/JavaScript
- **数字人引擎**：Live2D Cubism SDK
- **UI设计**：CSS3动画 + 渐变效果
- **响应式**：Flexbox布局 + 媒体查询

### 🎨 设计亮点
- **三栏自适应布局**：左侧信息栏、中间聊天区、右侧数字人
- **动画效果**：滑入动画、悬停效果、浮动动画
- **色彩搭配**：蓝紫渐变主题，现代化视觉效果
- **交互优化**：快捷按钮、实时反馈、流畅动画

### 🚀 性能优化
- **懒加载**：数字人资源按需加载
- **错误处理**：数字人库加载失败时的降级方案
- **兼容性**：支持主流浏览器
- **响应式**：移动端优化布局

## 页面布局

```
┌─────────────────────────────────────────────────────────┐
│                    鸿源技术学校AI助手                      │
├─────────────┬─────────────────────┬─────────────────────┤
│             │                     │                     │
│  左侧面板    │      中间聊天区      │    右侧数字人区      │
│             │                     │                     │
│ • 明学明事   │  • 学校logo         │  • Live2D数字人     │
│   明德明志   │  • 聊天消息区       │  • 动画效果         │
│ • 功能介绍   │  • 输入框           │  • 交互响应         │
│ • 快捷按钮   │  • 发送按钮         │                     │
│ • 用户信息   │                     │                     │
│             │                     │                     │
└─────────────┴─────────────────────┴─────────────────────┘
```

## 移动端适配

在移动设备上，布局会自动调整为垂直排列：

```
┌─────────────────────┐
│      左侧面板        │
├─────────────────────┤
│      聊天区域        │
├─────────────────────┤
│      数字人区域      │
└─────────────────────┘
```

## 自定义配置

### 修改数字人模型
1. 将Live2D模型文件放入相应目录
2. 修改JavaScript中的模型路径
3. 调整数字人容器样式

### 调整布局比例
```css
.left-panel { flex: 2; }    /* 左侧面板宽度 */
.chat-panel { flex: 3; }    /* 聊天区域宽度 */
.digital-human-panel { flex: 2; } /* 数字人区域宽度 */
```

### 自定义主题色彩
```css
/* 主要渐变色 */
background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

/* 次要渐变色 */
background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
```

## 注意事项

1. **数字人库依赖**：需要确保Live2D相关库文件正确加载
2. **浏览器兼容**：建议使用Chrome、Firefox、Safari等现代浏览器
3. **资源路径**：确保图片和脚本文件路径正确
4. **移动端优化**：在移动设备上会自动调整布局

## 后续扩展

### 可能的功能扩展
- [ ] 语音识别和语音合成
- [ ] 更多数字人模型选择
- [ ] 个性化设置面板
- [ ] 聊天记录保存
- [ ] 多语言支持
- [ ] 主题切换功能

### 技术优化
- [ ] 数字人动画优化
- [ ] 加载性能提升
- [ ] 错误处理完善
- [ ] 无障碍访问支持

## 联系方式

如有问题或建议，请联系：
- 技术支持：珠海龙马科技
- 邮箱：<EMAIL>
- 电话：13800138000

---

**祝您使用愉快！** 🎉
