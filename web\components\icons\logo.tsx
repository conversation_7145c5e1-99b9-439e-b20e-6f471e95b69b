export const LogoIcon = (props: React.SVGProps<SVGSVGElement>) => {
    return (
        <svg width="120" height="127" viewBox="0 0 120 127" fill="currentColor" xmlns="http://www.w3.org/2000/svg" {...props}>
            <rect x="26.5801" y="22.5773" width="10" height="40" rx="5" transform="rotate(20 26.5801 22.5773)" />
            <rect x="53.2295" y="6.70862" width="10" height="80" rx="5" transform="rotate(20 53.2295 6.70862)" />
            <rect x="91.1455" y="22.3624" width="10" height="80" rx="5" transform="rotate(20 91.1455 22.3624)" />
            <rect x="65.5112" y="34.3159" width="10" height="80" rx="5" transform="rotate(20 65.5112 34.3159)" />
            <rect x="101.389" y="52.6951" width="10" height="40" rx="5" transform="rotate(20 101.389 52.6951)" />
        </svg>
        
    )
}
