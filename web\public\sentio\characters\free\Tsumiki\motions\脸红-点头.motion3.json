{"Version": 3, "Meta": {"Duration": 3, "Fps": 30.0, "FadeInTime": 1.0, "FadeOutTime": 0.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 42, "TotalSegmentCount": 200, "TotalPointCount": 558, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.389, 0, 0.611, -15, 0.833, -15, 1, 0.878, -15, 0.922, -15, 0.967, -15, 1, 1.578, -15, 2.189, -15, 2.8, -15, 0, 3, -15]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.278, 0, 0.389, -25.185, 0.5, -25.185, 1, 0.611, -25.185, 0.722, -20, 0.833, -20, 1, 0.878, -20, 0.922, -20, 0.967, -20, 1, 1.044, -20, 1.122, 0, 1.2, 0, 1, 1.311, 0, 1.422, -20, 1.533, -20, 1, 1.611, -20, 1.689, -20, 1.767, -20, 1, 1.833, -20, 1.9, -10, 1.967, -10, 1, 2.067, -10, 2.167, -20, 2.267, -20, 1, 2.444, -20, 2.622, -20, 2.8, -20, 0, 3, -20]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 1.044, 0, 1.922, 0, 2.8, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": 0.2, "FadeOutTime": 0.2, "Segments": [0, 1, 1, 0.056, 1, 0.111, 1, 0.167, 1, 1, 0.222, 1, 0.278, 1, 0.333, 1, 1, 0.378, 1, 0.422, 0, 0.467, 0, 1, 0.478, 0, 0.489, 0, 0.5, 0, 1, 0.611, 0, 0.722, 0.65, 0.833, 0.65, 1, 1.156, 0.65, 1.478, 0.65, 1.8, 0.65, 1, 1.844, 0.65, 1.889, 0, 1.933, 0, 1, 1.944, 0, 1.956, 0, 1.967, 0, 1, 2.067, 0, 2.167, 0.65, 2.267, 0.65, 1, 2.444, 0.65, 2.622, 0.65, 2.8, 0.65, 0, 3, 0.65]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": 0.2, "FadeOutTime": 0.2, "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.278, 0, 0.389, 0, 0.5, 0, 1, 0.611, 0, 0.722, 1, 0.833, 1, 1, 1.156, 1, 1.478, 1, 1.8, 1, 1, 1.844, 1, 1.889, 0, 1.933, 0, 1, 1.944, 0, 1.956, 0, 1.967, 0, 1, 2.067, 0, 2.167, 1, 2.267, 1, 1, 2.444, 1, 2.622, 1, 2.8, 1, 0, 3, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": 0.2, "FadeOutTime": 0.2, "Segments": [0, 1, 1, 0.056, 1, 0.111, 1, 0.167, 1, 1, 0.222, 1, 0.278, 1, 0.333, 1, 1, 0.378, 1, 0.422, 0, 0.467, 0, 1, 0.478, 0, 0.489, 0, 0.5, 0, 1, 0.611, 0, 0.722, 0.65, 0.833, 0.65, 1, 1.156, 0.65, 1.478, 0.65, 1.8, 0.65, 1, 1.844, 0.65, 1.889, 0, 1.933, 0, 1, 1.944, 0, 1.956, 0, 1.967, 0, 1, 2.067, 0, 2.167, 0.65, 2.267, 0.65, 1, 2.444, 0.65, 2.622, 0.65, 2.8, 0.65, 0, 3, 0.65]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": 0.2, "FadeOutTime": 0.2, "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.278, 0, 0.389, 0, 0.5, 0, 1, 0.611, 0, 0.722, 1, 0.833, 1, 1, 1.156, 1, 1.478, 1, 1.8, 1, 1, 1.844, 1, 1.889, 0, 1.933, 0, 1, 1.944, 0, 1.956, 0, 1.967, 0, 1, 2.067, 0, 2.167, 1, 2.267, 1, 1, 2.444, 1, 2.622, 1, 2.8, 1, 0, 3, 1]}, {"Target": "Parameter", "Id": "ParamTear", "Segments": [0, 1, 0, 3, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.278, 0, 0.389, 0, 0.5, 0, 1, 0.611, 0, 0.722, -1, 0.833, -1, 1, 1.489, -1, 2.144, -1, 2.8, -1, 0, 3, -1]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, -0.5, 1, 0.056, -0.5, 0.111, -0.5, 0.167, -0.5, 1, 0.278, -0.5, 0.389, -0.5, 0.5, -0.5, 1, 0.611, -0.5, 0.722, -1, 0.833, -1, 1, 1.489, -1, 2.144, -1, 2.8, -1, 0, 3, -1]}, {"Target": "Parameter", "Id": "ParamEyeBallForm", "Segments": [0, 1, 1, 0.056, 1, 0.111, 1, 0.167, 1, 1, 1.044, 1, 1.922, 1, 2.8, 1, 0, 3, 1]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.356, 0, 0.544, -0.3, 0.733, -0.3, 1, 1.422, -0.3, 2.111, -0.3, 2.8, -0.3, 0, 3, -0.3]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.356, 0, 0.544, -0.3, 0.733, -0.3, 1, 1.422, -0.3, 2.111, -0.3, 2.8, -0.3, 0, 3, -0.3]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.222, 0, 0.278, 0, 0.333, 0, 1, 0.544, 0, 0.756, 0, 0.967, 0, 1, 1.156, 0, 1.344, -1, 1.533, -1, 1, 1.956, -1, 2.378, -1, 2.8, -1, 0, 3, -1]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.222, 0, 0.278, 0, 0.333, 0, 1, 0.544, 0, 0.756, 0, 0.967, 0, 1, 1.156, 0, 1.344, -1, 1.533, -1, 1, 1.956, -1, 2.378, -1, 2.8, -1, 0, 3, -1]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.222, 0, 0.278, 0, 0.333, 0, 1, 1.156, 0, 1.978, 0, 2.8, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.222, 0, 0.278, 0, 0.333, 0, 1, 1.156, 0, 1.978, 0, 2.8, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.222, 0, 0.278, -1, 0.333, -1, 1, 1.156, -1, 1.978, -1, 2.8, -1, 0, 3, -1]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.222, 0, 0.278, -1, 0.333, -1, 1, 1.156, -1, 1.978, -1, 2.8, -1, 0, 3, -1]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 1, 0.056, 1, 0.111, 1, 0.167, 1, 1, 0.244, 1, 0.322, 0, 0.4, 0, 1, 0.711, 0, 1.022, 0, 1.333, 0, 1, 1.444, 0, 1.556, 0.2, 1.667, 0.2, 1, 1.722, 0.2, 1.778, 0.2, 1.833, 0.2, 1, 2.156, 0.2, 2.478, 0.2, 2.8, 0.2, 0, 3, 0.2]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.244, 0, 0.322, 0.72, 0.4, 0.72, 1, 0.711, 0.72, 1.022, 0.72, 1.333, 0.72, 1, 1.444, 0.72, 1.556, 0, 1.667, 0, 1, 1.722, 0, 1.778, 0, 1.833, 0, 1, 2.156, 0, 2.478, 0, 2.8, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamCheek_01", "Segments": [0, 1, 0, 3, 1]}, {"Target": "Parameter", "Id": "ParamCheek_03", "Segments": [0, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamCheek_04", "Segments": [0, 1, 0, 3, 1]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.2, 0, 0.233, 0, 0.267, 0, 1, 0.478, 0, 0.689, -5, 0.9, -5, 1, 1.533, -5, 2.167, -5, 2.8, -5, 0, 3, -5]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.433, 0, 0.7, 0, 0.967, 0, 1, 1.044, 0, 1.122, 7, 1.2, 7, 1, 1.311, 7, 1.422, 0, 1.533, 0, 1, 1.622, 0, 1.711, 0, 1.8, 0, 1, 1.867, 0, 1.933, 5, 2, 5, 1, 2.1, 5, 2.2, 0, 2.3, 0, 1, 2.467, 0, 2.633, 0, 2.8, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.222, 0, 0.278, 0, 0.333, 0, 1, 1.156, 0, 1.978, 0, 2.8, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 1.044, 0, 1.922, 0, 2.8, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "Param<PERSON><PERSON><PERSON>", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 1.044, 0, 1.922, 0, 2.8, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamBustY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 1.044, 0, 1.922, 0, 2.8, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamTie", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 1.044, 0, 1.922, 0, 2.8, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamSkirt", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 1.044, 0, 1.922, 0, 2.8, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 1.044, 0, 1.922, 0, 2.8, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamHairSide", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 1.044, 0, 1.922, 0, 2.8, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamHairAho", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.3, 0, 0.433, 1, 0.567, 1, 1, 0.7, 1, 0.833, 1, 0.967, 1, 1, 1.056, 1, 1.144, -0.2, 1.233, -0.2, 1, 1.344, -0.2, 1.456, 1, 1.567, 1, 1, 1.644, 1, 1.722, 1, 1.8, 1, 1, 1.889, 1, 1.978, 0, 2.067, 0, 1, 2.178, 0, 2.289, 1, 2.4, 1, 1, 2.533, 1, 2.667, 1, 2.8, 1, 0, 3, 1]}, {"Target": "Parameter", "Id": "ParamHairTair", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0.05, 0.167, 0.05, 1, 0.389, 0.05, 0.611, -1, 0.833, -1, 1, 0.967, -1, 1.1, 0, 1.233, 0, 1, 1.344, 0, 1.456, -1, 1.567, -1, 1, 1.644, -1, 1.722, -1, 1.8, -1, 1, 1.889, -1, 1.978, -0.25, 2.067, -0.25, 1, 2.178, -0.25, 2.289, -1, 2.4, -1, 1, 2.533, -1, 2.667, -1, 2.8, -1, 0, 3, -1]}, {"Target": "Parameter", "Id": "ParamRibonL", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 1.044, 0, 1.922, 0, 2.8, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamRibonR", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 1.044, 0, 1.922, 0, 2.8, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamArm", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 1.044, 0, 1.922, 0, 2.8, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamArmL", "Segments": [0, 1, 1, 0.933, 1, 1.867, 1, 2.8, 1, 0, 3, 1]}, {"Target": "Parameter", "Id": "ParamArmR", "Segments": [0, 1, 1, 0.933, 1, 1.867, 1, 2.8, 1, 0, 3, 1]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0.5, 1, 0.056, 0.5, 0.111, 0.5, 0.167, 0.5, 1, 1.044, 0.5, 1.922, 0.5, 2.8, 0.5, 0, 3, 0.5]}]}